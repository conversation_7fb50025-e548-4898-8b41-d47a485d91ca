package com.lanhu.lims.gateway.admin.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @description: 委托单详情顶栏信息详情
 * @author: huangzheng
 * @date: 2025/6/11 15:14
 */


@Data
@ApiModel(value = "委托单详情顶栏信息详情", description = "委托单详情顶栏信息详情")
public class EntrustOrderBannerDetailVO {



    /**
     * 委托单编号
     */
    @ApiModelProperty(value="委托单编号")
    private String entrustOrderNo;



    /**
     * 委托单整体状态
     */
    @ApiModelProperty(value="全局状态状态:(-1:已终止 0:待提交 1:审核中 2:进行中 7:已完成）")
    private Integer globalStatus;



    /**
     * 委托单当前步骤，既委托单的状态
     */
    @ApiModelProperty(value="-1:已终止 0:待提交 1:审核中 2:样品入库中 3:任务分配中 4:样品检测中 5:检测结果录入中 6: 报告生成中 7:已完成")
    private Integer status;






     /**
      * 检测项目，多个用、隔开
      */
     @ApiModelProperty(value="检测项目")
     private String inspectItems;



     /**
      * 关联任务负责人，多个用、隔开
      */
     @ApiModelProperty(value="关联任务负责人，检测员，多个用、隔开")
     private String handler_names;



     @ApiModelProperty(value = "期限交付日期")
     private String planCompleteDate;



    @ApiModelProperty(value = "期限交付日期")
    private String completeDate;



    @ApiModelProperty(value = "终止原因")
    private String terminateReason;



























}
