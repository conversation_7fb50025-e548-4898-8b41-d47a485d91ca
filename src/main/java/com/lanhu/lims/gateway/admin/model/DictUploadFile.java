package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上传文件配置表
 */
@ApiModel(value = "上传文件配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_dict_upload_file")
public class DictUploadFile {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 文件类型
     */
    @TableField(value = "file_type")
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 可上传文件数量
     */
    @TableField(value = "file_size")
    @ApiModelProperty(value = "可上传文件数量")
    private Integer fileSize;

    /**
     * 可上传单个文件大小
     */
    @TableField(value = "file_max")
    @ApiModelProperty(value = "可上传单个文件大小")
    private Integer fileMax;

    /**
     * 文件后缀
     */
    @TableField(value = "file_suffix")
    @ApiModelProperty(value = "文件后缀")
    private String fileSuffix;

    /**
     * 上传目录
     */
    @TableField(value = "file_upload_dir")
    @ApiModelProperty(value = "上传目录")
    private String fileUploadDir;

    /**
     * 文件访问权限(0:直接访问1:签名访问 2:下载访问)
     */
    @TableField(value = "file_acl")
    @ApiModelProperty(value = "文件访问权限(0:直接访问1:签名访问 2:下载访问)")
    private Integer fileAcl;

    /**
     * 访问地址
     */
    @TableField(value = "url")
    @ApiModelProperty(value = "访问地址")
    private String url;

    /**
     * 备注说明
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注说明")
    private String remark;
}