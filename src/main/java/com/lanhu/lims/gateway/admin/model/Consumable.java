package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/********************************
 * @title Consumable
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 13:48
 * @version 0.0.1
 *********************************/

@ApiModel(description = "耗材表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_consumable")
public class Consumable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 耗材编号（格式示例：HC0001）
     */
    @TableField(value = "consumable_code")
    @ApiModelProperty(value = "耗材编号（格式示例：HC0001）")
    private String consumableCode;

    /**
     * 耗材名称
     */
    @TableField(value = "consumable_name")
    @ApiModelProperty(value = "耗材名称")
    private String consumableName;

    /**
     * 耗材简称
     */
    @TableField(value = "short_name")
    @ApiModelProperty(value = "耗材简称")
    private String shortName;

    /**
     * 启用状态：0->禁用；1->启用
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "启用状态：0->禁用；1->启用")
    private Integer status;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    @JsonIgnore
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    @JsonIgnore
    private Integer auditStatus;

    /**
     * 最近一次审核人ID
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "最近一次审核人ID")
    @JsonIgnore
    private Long auditBy;

    /**
     * 最近一次审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "最近一次审核时间")
    @JsonIgnore
    private Date auditTime;

    /**
     * 最近一次审核人名称
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "最近一次审核人名称")
    @JsonIgnore
    private String auditName;

    /**
     * 审核备注/审核不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "审核备注/审核不通过原因")
    @JsonIgnore
    private String auditRemark;

    /**
     * 耗材规格列表（非数据库字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "耗材规格列表")
    private List<ConsumableSpecification> specifications;
}