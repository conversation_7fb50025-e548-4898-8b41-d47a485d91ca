package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/********************************
 * @title ReagentSpecificationSingleForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 查询试剂规格详情入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "查询试剂规格详情入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReagentSpecificationSingleForm {
    /**
     * 规格ID
     */
    @ApiModelProperty(value = "规格ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long id;
}
