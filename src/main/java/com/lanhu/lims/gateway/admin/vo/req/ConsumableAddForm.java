package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/********************************
 * @title ConsumableAddForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 新增耗材入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "新增耗材入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumableAddForm {
    /**
     * 耗材名称
     */
    @ApiModelProperty(value = "耗材名称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String consumableName;

    /**
     * 耗材简称
     */
    @ApiModelProperty(value = "耗材简称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String shortName;
}
