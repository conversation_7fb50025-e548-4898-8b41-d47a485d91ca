package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/********************************
 * @title ConsumableSpecificationListForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 查询耗材规格列表入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "查询耗材规格列表入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumableSpecificationListForm {
    /**
     * 耗材ID
     */
    @ApiModelProperty(value = "耗材ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long consumableId;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 货号
     */
    @ApiModelProperty(value = "货号")
    private String catalogNumber;

    /**
     * 规格等
     */
    @ApiModelProperty(value = "规格等")
    private String specification;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unit;

    /**
     * 存储条件
     */
    @ApiModelProperty(value = "存储条件")
    private String storageCondition;

    /**
     * 材质
     */
    @ApiModelProperty(value = "材质")
    private String material;
}
