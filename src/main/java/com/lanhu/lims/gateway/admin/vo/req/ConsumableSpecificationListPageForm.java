package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lanhu.lims.gateway.admin.core.PageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/********************************
 * @title ConsumableSpecificationListPageForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 查询耗材规格分页列表入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "查询耗材规格分页列表入参")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumableSpecificationListPageForm extends PageForm {
    /**
     * 耗材ID
     */
    @ApiModelProperty(value = "耗材ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long consumableId;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 货号
     */
    @ApiModelProperty(value = "货号")
    private String catalogNumber;

    /**
     * 规格等
     */
    @ApiModelProperty(value = "规格等")
    private String specification;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unit;

    /**
     * 存储条件
     */
    @ApiModelProperty(value = "存储条件")
    private String storageCondition;

    /**
     * 材质
     */
    @ApiModelProperty(value = "材质")
    private String material;
}
