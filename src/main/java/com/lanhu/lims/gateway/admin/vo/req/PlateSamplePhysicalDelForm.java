package com.lanhu.lims.gateway.admin.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description: 物理删除一个96孔板上的所有样本
 * @author: huangzheng
 * @date: 2025/6/17 10:23
 */

@Data
@ApiModel(value = "物理删除一个96孔板上的所有样本")
public class PlateSamplePhysicalDelForm
{


    @ApiModelProperty(value = "96孔板号", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String plateNum;






}
