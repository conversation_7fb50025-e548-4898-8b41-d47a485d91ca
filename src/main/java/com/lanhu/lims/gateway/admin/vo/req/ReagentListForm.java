package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/********************************
 * @title ReagentListForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 查询试剂列表入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "查询试剂列表入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReagentListForm {
    /**
     * 试剂编号
     */
    @ApiModelProperty(value = "试剂编号")
    private String code;

    /**
     * 试剂名称
     */
    @ApiModelProperty(value = "试剂名称")
    private String name;

    /**
     * 试剂简称
     */
    @ApiModelProperty(value = "试剂简称")
    private String shortName;

    /**
     * 状态：0: 禁用 1: 启用
     */
    @ApiModelProperty(value = "状态：0: 禁用 1: 启用")
    private Integer status;
}
