package com.lanhu.lims.gateway.admin.core;

/********************************
 * @title ProjectConstant
 * @package com.lanhu.imenu.gateway.client.core
 * @description description
 *
 * @date 2022/11/17 2:19 下午
 * @version 0.0.1
 *********************************/
public class ProjectConstant {

    /**
     * 逗号
     */
    public static final String COMMA = ",";

    /**
     * 冒号
     */
    public static final String COLON = ":";

    /**
     * 分号
     */
    public static final String SEMICOLON = ";";
    /**
     * 点
     */
    public static final String DOT = ".";

    /**
     * 空字符
     */
    public static final String EMPTY = "";


    public  static final String UTF8 ="UTF-8";

    /**
     * 默认DB的key
     */
    public static final String DEFAULT_DB_KEY = "0";

    /**
     * 下划线
     */
    public static final String UNDER_LINE = "_";




    /**
     * 请求头标记——language
     */
    public static final String HEADER_LANGUAGE_FLAG = "LANGUAGE";



    public static final Long SUPER_ADMIN_ROLE_ID = 1L;



    public static final String USER_RESET_PWD = "Abc123";


    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";



    public static final String MEMBER_USER_LOGIN_PWD = "000000";



    /**
    * @description: ts批量插入
    * @param: 
    * @return: 
    * @author: liuyi
    * @date: 3:59 下午 2023/6/3 
    */
    public static final int TS_BATCH_INSERT = 200;


    /**
     * 日期格式化
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";


    public static final String DATE_FORMAT = "yyyy-MM-dd";


    public static  final String STAR = "*";


    /**
    * @description: 10秒
    * @param: 
    * @return: 
    * @author: liuyi
    * @date: 8:11 下午 2023/6/24 
    */
    public static final Long DELAY_TIME = 10000L;


    /**
    * @description: 减号
    * @param: 
    * @return: 
    * @author: liuyi
    * @date: 8:42 上午 2023/7/6 
    */
    public static final String MINUS = "-";


    /**
    * @description: 加号
    * @param: 
    * @return: 
    * @author: liuyi
    * @date: 8:45 上午 2023/7/6
    */
    public static final String ADD = "+";


    public static final String DEFAULT_LANGUAGE = "en_US";


    public static final String ADMIN = "admin";

    public static final String TASK_TERMINATION = "任务终止";
    public static final String TASK_REVOCATION = "任务撤销";


    /**
     * @description: 流程流转携带的参数名
     *
     */
    public static final String FLAG = "flag";


    /**
     * @description: 默认结束节点的code
     *
     */

    public static final String DEFAULT_END_NODE_CODE = "end";

    /**
     * @description: 默认开始节点的code
     *
     */
    public static final String DEFAULT_START_NODE_CODE = "start";

    //换行符
    public static final String LINE_BREAK = "\n";


    /**
     * @description: 左斜杠
     */

    public static final String LEFT_SLASH = "/";

    /**
     * @description: 顿号
     */
    public static final String PAUSE = "、";








}
