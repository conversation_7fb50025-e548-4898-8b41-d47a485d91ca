package com.lanhu.lims.gateway.admin.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 96孔板出参
 * @author: huangzheng
 * @date: 2025/6/11 13:19
 */

@Data
@ApiModel(value = "96孔板出参",description = "96孔板出参")
public class EntrustOrderPlateVO {


    /**
     * 板号
     */
    @ApiModelProperty(value="板号")
    private String plateNum;


    /**
     * 样本数量
     */

    @ApiModelProperty(value="样本数量")
    private Integer sampleNum;



    /**
     * 样本列表
     */
    @ApiModelProperty(value="样本列表")
    List<EntrustOrderPlateSampleVO> plateSampleList;




}
