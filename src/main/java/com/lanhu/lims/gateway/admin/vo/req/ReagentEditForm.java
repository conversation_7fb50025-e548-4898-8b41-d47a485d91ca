package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/********************************
 * @title ReagentEditForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 修改试剂入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "修改试剂入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReagentEditForm {
    /**
     * 试剂ID
     */
    @ApiModelProperty(value = "试剂ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long id;

    /**
     * 试剂名称
     */
    @ApiModelProperty(value = "试剂名称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String name;

    /**
     * 试剂简称
     */
    @ApiModelProperty(value = "试剂简称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String shortName;
}
