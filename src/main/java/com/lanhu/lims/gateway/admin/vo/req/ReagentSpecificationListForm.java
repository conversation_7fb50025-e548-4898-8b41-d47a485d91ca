package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/********************************
 * @title ReagentSpecificationListForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 查询试剂规格列表入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "查询试剂规格列表入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReagentSpecificationListForm {
    /**
     * 试剂ID
     */
    @ApiModelProperty(value = "试剂ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long reagentId;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 货号
     */
    @ApiModelProperty(value = "货号")
    private String catalogNumber;

    /**
     * 规格等
     */
    @ApiModelProperty(value = "规格等")
    private String specification;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unit;

    /**
     * 存储条件
     */
    @ApiModelProperty(value = "存储条件")
    private String storageCondition;
}
