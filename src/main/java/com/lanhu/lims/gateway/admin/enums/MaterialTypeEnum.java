package com.lanhu.lims.gateway.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/********************************
 * @title MaterialTypeEnum
 * @package com.lanhu.lims.gateway.admin.enums
 * @description 附件类型枚举
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@Getter
@AllArgsConstructor
public enum MaterialTypeEnum {
    /**
     * 设备仪器
     */
    EQUIPMENT(0, "设备仪器"),

    /**
     * 试剂
     */
    REAGENT(1, "试剂"),

    /**
     * 耗材
     */
    CONSUMABLE(2, "耗材"),

    /**
     * 维护记录
     */
    MAINTENANCE_RECORD(3, "维护记录"),

    ;

    private final int code;

    private final String msg;

    public static MaterialTypeEnum convert(int code) {
        for (MaterialTypeEnum value : MaterialTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }
}
