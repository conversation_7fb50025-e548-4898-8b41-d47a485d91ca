package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/********************************
 * @title ConsumableEditForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 修改耗材入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "修改耗材入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumableEditForm {
    /**
     * 耗材ID
     */
    @ApiModelProperty(value = "耗材ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long id;

    /**
     * 耗材名称
     */
    @ApiModelProperty(value = "耗材名称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String consumableName;

    /**
     * 耗材简称
     */
    @ApiModelProperty(value = "耗材简称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String shortName;
}
