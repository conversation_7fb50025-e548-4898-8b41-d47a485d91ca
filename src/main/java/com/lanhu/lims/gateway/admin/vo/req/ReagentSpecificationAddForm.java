package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/********************************
 * @title ReagentSpecificationAddForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 新增试剂规格入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "新增试剂规格入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReagentSpecificationAddForm {
    /**
     * 试剂ID
     */
    @ApiModelProperty(value = "试剂ID", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long reagentId;

    /**
     * 品牌（字典值）
     */
    @ApiModelProperty(value = "品牌（字典值）", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String brand;

    /**
     * 货号
     */
    @ApiModelProperty(value = "货号")
    private String catalogNumber;

    /**
     * 规格等
     */
    @ApiModelProperty(value = "规格等")
    private String specification;

    /**
     * 计量单位（字典值）
     */
    @ApiModelProperty(value = "计量单位（字典值）", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String unit;

    /**
     * 存储条件（字典值）
     */
    @ApiModelProperty(value = "存储条件（字典值）", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String storageCondition;
}
