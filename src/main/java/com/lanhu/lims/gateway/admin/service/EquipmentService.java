package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.*;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.utils.validation.DataChangeAnalyzer;
import com.lanhu.lims.gateway.admin.vo.req.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/********************************
 * @title EquipmentService
 * @package com.lanhu.lims.gateway.admin.service
 * @description 仪器设备服务类
 *
 * <AUTHOR>
 * @date 2025/6/14 22:25
 * @version 0.0.1
 *********************************/
@Service
public class EquipmentService {
    @Resource
    private EquipmentMapper equipmentMapper;

    @Resource
    private EquipmentMaintenanceRecordMapper equipmentMaintenanceRecordMapper;

    @Resource
    private DetectionMethodMapper detectionMethodMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    @Resource
    private FileRecordMapper fileRecordMapper;

    @Resource
    private MaterialAttachmentMapper materialAttachmentMapper;

    /**
     * 查询仪器设备列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public PcsResult<List<Equipment>> list(EquipmentListForm form) {
        LambdaQueryWrapper<Equipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Equipment::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentCode()), Equipment::getEquipmentCode, form.getEquipmentCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentName()), Equipment::getEquipmentName, form.getEquipmentName());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), Equipment::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getModel()), Equipment::getModel, form.getModel());
        wrapper.eq(form.getStatus() != null, Equipment::getStatus, form.getStatus());
        wrapper.orderByDesc(Equipment::getCreateTime);
        List<Equipment> equipmentList = equipmentMapper.selectList(wrapper);
        // 填充扩展字段
        fillExtraField(equipmentList);
        return Result.ok(equipmentList);
    }

    /**
     * 分页查询仪器设备
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public PcsResult<IPage<Equipment>> listPage(EquipmentListPageForm form) {
        LambdaQueryWrapper<Equipment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Equipment::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentCode()), Equipment::getEquipmentCode, form.getEquipmentCode());
        wrapper.like(StrUtil.isNotBlank(form.getEquipmentName()), Equipment::getEquipmentName, form.getEquipmentName());
        wrapper.like(StrUtil.isNotBlank(form.getBrand()), Equipment::getBrand, form.getBrand());
        wrapper.like(StrUtil.isNotBlank(form.getModel()), Equipment::getModel, form.getModel());
        wrapper.eq(form.getStatus() != null, Equipment::getStatus, form.getStatus());
        wrapper.eq(form.getOwnershipStatus() != null, Equipment::getOwnershipStatus, form.getOwnershipStatus());
        wrapper.orderByDesc(Equipment::getCreateTime);
        Page<Equipment> page = new Page<>(form.getPageIndex(), form.getPageSize());
        IPage<Equipment> pageInfo = equipmentMapper.selectPage(page, wrapper);
        // 填充扩展字段
        fillExtraField(pageInfo.getRecords());
        return Result.ok(pageInfo);
    }

    /**
     * 查询仪器设备详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public PcsResult<Equipment> detail(EquipmentSingleForm form) {
        Equipment equipment = equipmentMapper.selectById(form.getId());
        if (equipment == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), equipment.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EQUIPMENT_NOT_EXIST);
        }
        // 填充扩展字段
        fillExtraField(CollUtil.newArrayList(equipment));
        return Result.ok(equipment);
    }

    /**
     * 新增仪器设备
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> add(EquipmentAddForm form, AdminUser adminUser) {
        // 生成设备编号
        String equipmentCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.EQUIPMENT_CODE);
        // 获取文件记录
        List<FileRecord> fileRecords = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(form.getFileRecordIds())) {
            for (Long fileRecordId : form.getFileRecordIds()) {
                FileRecord fileRecord = fileRecordMapper.selectById(fileRecordId);
                if (fileRecord == null) {
                    throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
                }
                fileRecords.add(fileRecord);
            }
        }
        // 新增仪器设备
        Equipment equipment = new Equipment();
        equipment.setEquipmentCode(equipmentCode);
        equipment.setEquipmentName(form.getEquipmentName());
        equipment.setUsage(StrUtil.blankToDefault(form.getUsage(), StrUtil.EMPTY));
        equipment.setBrand(StrUtil.blankToDefault(form.getBrand(), StrUtil.EMPTY));
        equipment.setModel(StrUtil.blankToDefault(form.getModel(), StrUtil.EMPTY));
        equipment.setOwnershipStatus(form.getOwnershipStatus());
        equipment.setInspectItem(StrUtil.blankToDefault(form.getInspectItem(), StrUtil.EMPTY));
        equipment.setConsignee(StrUtil.blankToDefault(form.getConsignee(), StrUtil.EMPTY));
        equipment.setReceiptDate(StrUtil.blankToDefault(form.getReceiptDate(), StrUtil.EMPTY));
        equipment.setStatus(EquipmentStatusEnum.NORMAL.getCode());
        equipment.setIsEffect(IsEffectEnum.NORMAL.getCode());
        equipment.setCreateBy(adminUser.getId());
        equipment.setCreateName(adminUser.getRealName());
        equipment.setCreateTime(DateUtil.date());
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(equipment.getCreateTime());
        equipment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        equipment.setAuditName(StrUtil.EMPTY);
        equipment.setAuditBy(null);
        equipment.setAuditTime(null);
        equipment.setAuditRemark(StrUtil.EMPTY);
        equipmentMapper.insert(equipment);
        // 保存附件
        for (FileRecord fileRecord : fileRecords) {
            MaterialAttachment materialAttachment = new MaterialAttachment();
            materialAttachment.setId(IdUtil.getSnowflakeNextId());
            materialAttachment.setMaterialId(equipment.getId());
            materialAttachment.setMaterialType(MaterialTypeEnum.EQUIPMENT.getCode());
            materialAttachment.setFileId(fileRecord.getId());
            materialAttachment.setAttachmentName(fileRecord.getAttachmentName());
            materialAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
            materialAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
            materialAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
            materialAttachment.setIsEffect(IsEffectEnum.NORMAL.getCode());
            materialAttachment.setCreateBy(adminUser.getId());
            materialAttachment.setCreateName(adminUser.getRealName());
            materialAttachment.setCreateTime(DateUtil.date());
            materialAttachment.setUpdateBy(adminUser.getId());
            materialAttachment.setUpdateName(adminUser.getRealName());
            materialAttachment.setUpdateTime(materialAttachment.getCreateTime());
            materialAttachmentMapper.insert(materialAttachment);
        }
        // todo 发起流程
        return Result.ok();
    }

    /**
     * 修改仪器设备
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> edit(EquipmentEditForm form, AdminUser adminUser) {
        // 校验数据
        Equipment equipment = checkCanEditOrDelete(form.getId());
        // 获取附件
        List<MaterialAttachment> equipmentAttachments = materialAttachmentMapper.selectList(Wrappers.<MaterialAttachment>lambdaQuery().eq(MaterialAttachment::getMaterialId, equipment.getId()).eq(MaterialAttachment::getIsEffect, IsEffectEnum.NORMAL.getCode()));
        List<Long> fileIds = equipmentAttachments.stream().map(MaterialAttachment::getFileId).collect(Collectors.toList());
        // 比对信息
        Map<String, String> allDictMap = getDictDataMap(false);
        Map<String, String> enableDictMap = getDictDataMap(true);
        DataChangeAnalyzer.DataChangeResult<String> usageChangeResult = analyzeDictDataChange(equipment.getUsage(), form.getUsage(), allDictMap, enableDictMap, DictDataTypeEnum.EQUIPMENT_USAGE);
        DataChangeAnalyzer.DataChangeResult<String> brandChangeResult = analyzeDictDataChange(equipment.getBrand(), form.getBrand(), allDictMap, enableDictMap, DictDataTypeEnum.EQUIPMENT_BRAND);
        DataChangeAnalyzer.DataChangeResult<String> ownershipStatusChangeResult = analyzeDictDataChange(String.valueOf(equipment.getOwnershipStatus()), String.valueOf(form.getOwnershipStatus()), allDictMap, enableDictMap, DictDataTypeEnum.EQUIPMENT_OWNERSHIP_STATUS);
        DataChangeAnalyzer.DataChangeResult<String> statusChangeResult = analyzeDictDataChange(String.valueOf(equipment.getStatus()), String.valueOf(form.getStatus()), allDictMap, enableDictMap, DictDataTypeEnum.EQUIPMENT_STATUS);
        DataChangeAnalyzer.DataChangeResult<Long> inspectItemChangeResult = DataChangeAnalyzer.analyzeLongChange(equipment.getInspectItem(), form.getInspectItem());
        DataChangeAnalyzer.DataChangeResult<Long> fileIdChangeResult = DataChangeAnalyzer.analyzeListChange(fileIds, form.getFileRecordIds());
        // 修改仪器设备
        equipment.setEquipmentName(form.getEquipmentName());
        equipment.setUsage(StrUtil.blankToDefault(form.getUsage(), StrUtil.EMPTY));
        equipment.setBrand(StrUtil.blankToDefault(form.getBrand(), StrUtil.EMPTY));
        equipment.setModel(StrUtil.blankToDefault(form.getModel(), StrUtil.EMPTY));
        equipment.setOwnershipStatus(form.getOwnershipStatus());
        equipment.setInspectItem(StrUtil.blankToDefault(form.getInspectItem(), StrUtil.EMPTY));
        equipment.setConsignee(StrUtil.blankToDefault(form.getConsignee(), StrUtil.EMPTY));
        equipment.setReceiptDate(StrUtil.blankToDefault(form.getReceiptDate(), StrUtil.EMPTY));
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(DateUtil.date());
        equipment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        equipmentMapper.updateById(equipment);
        // 保存附件
        if (CollUtil.isNotEmpty(fileIdChangeResult.getRemovedItems())) {
            // 删除附件
            materialAttachmentMapper.update(null, Wrappers.<MaterialAttachment>lambdaUpdate().eq(MaterialAttachment::getMaterialId, equipment.getId()).in(MaterialAttachment::getFileId, fileIdChangeResult.getRemovedItems()).set(MaterialAttachment::getIsEffect, IsEffectEnum.DELETE.getCode()));
        }
        if (CollUtil.isNotEmpty(fileIdChangeResult.getAddedItems())) {
            // 新增附件
            for (Long fileId : fileIdChangeResult.getAddedItems()) {
                FileRecord fileRecord = fileRecordMapper.selectById(fileId);
                if (fileRecord == null) {
                    throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
                }
                MaterialAttachment equipmentAttachment = new MaterialAttachment();
                equipmentAttachment.setId(IdUtil.getSnowflakeNextId());
                equipmentAttachment.setMaterialId(equipment.getId());
                equipmentAttachment.setFileId(fileId);
                equipmentAttachment.setAttachmentName(fileRecord.getAttachmentName());
                equipmentAttachment.setAttachmentUrl(fileRecord.getAttachmentUrl());
                equipmentAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
                equipmentAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());
                equipmentAttachment.setIsEffect(IsEffectEnum.NORMAL.getCode());
                equipmentAttachment.setCreateBy(adminUser.getId());
                equipmentAttachment.setCreateName(adminUser.getRealName());
                equipmentAttachment.setCreateTime(DateUtil.date());
                equipmentAttachment.setUpdateBy(adminUser.getId());
                equipmentAttachment.setUpdateName(adminUser.getRealName());
                equipmentAttachment.setUpdateTime(equipmentAttachment.getCreateTime());
                materialAttachmentMapper.insert(equipmentAttachment);
            }
        }
        // todo 发起流程
        return Result.ok();
    }

    /**
     * 删除仪器设备
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> del(EquipmentDelForm form, AdminUser adminUser) {
        // 校验数据
        Equipment equipment = checkCanEditOrDelete(form.getId());
        // 删除仪器设备
        equipment.setIsEffect(IsEffectEnum.DELETE.getCode());
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(DateUtil.date());
        equipment.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        equipmentMapper.updateById(equipment);
        // todo 发起流程
        return Result.ok();
    }

    /**
     * 设备维护
     *
     * @param form      维护入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> maintenance(EquipmentMaintenanceForm form, AdminUser adminUser) {
        // 校验数据
        Equipment equipment = equipmentMapper.selectById(form.getId());
        if (equipment == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), equipment.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EQUIPMENT_NOT_EXIST);
        }
        // 创建维护记录
        EquipmentMaintenanceRecord maintenanceRecord = new EquipmentMaintenanceRecord();
        maintenanceRecord.setEquipmentId(form.getId());
        maintenanceRecord.setMaintainerId(adminUser.getId());
        maintenanceRecord.setMaintainerName(adminUser.getRealName());
        maintenanceRecord.setMaintenanceContent(form.getMaintenanceContent());
        maintenanceRecord.setMaintenanceTime(form.getMaintenanceTime());
        maintenanceRecord.setNextMaintenanceTime(form.getNextMaintenanceTime());
        maintenanceRecord.setIsEffect(IsEffectEnum.NORMAL.getCode());
        maintenanceRecord.setCreateBy(adminUser.getId());
        maintenanceRecord.setCreateName(adminUser.getRealName());
        maintenanceRecord.setCreateTime(DateUtil.date());
        maintenanceRecord.setUpdateBy(adminUser.getId());
        maintenanceRecord.setUpdateName(adminUser.getRealName());
        maintenanceRecord.setUpdateTime(maintenanceRecord.getCreateTime());
        equipmentMaintenanceRecordMapper.insert(maintenanceRecord);
        // 更新维护人
        equipment.setMaintenanceUser(adminUser.getRealName());
        equipment.setUpdateBy(adminUser.getId());
        equipment.setUpdateName(adminUser.getRealName());
        equipment.setUpdateTime(DateUtil.date());
        equipmentMapper.updateById(equipment);
        return Result.ok();
    }

    // =================================================================== 工作流回调 =================================================================== //

    // =================================================================== 私有方法区 =================================================================== //

    /**
     * 校验是否可以编辑或删除
     *
     * @param id 仪器设备ID
     * @return 仪器设备信息
     */
    private Equipment checkCanEditOrDelete(Long id) {
        Equipment equipment = equipmentMapper.selectById(id);
        if (equipment == null || ObjectUtil.notEqual(IsEffectEnum.NORMAL.getCode(), equipment.getIsEffect())) {
            throw new BusinessException(PcsResultCode.EQUIPMENT_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(AuditStatusEnum.PASS.getCode(), equipment.getAuditStatus())) {
            throw new BusinessException(PcsResultCode.DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE);
        }
        return equipment;
    }

    /**
     * 填充拓展字段
     *
     * @param equipmentList 设备信息列表
     */
    public void fillExtraField(List<Equipment> equipmentList) {
        if (CollUtil.isEmpty(equipmentList)) {
            return;
        }
        // 获取所有字典数据
        Map<String, String> dictMap = getDictDataMap(false);
        // 获取检测方法列表
        List<Long> inspectItemIds = equipmentList.stream().map(Equipment::getInspectItem).filter(StrUtil::isNotBlank).map(inspectItem -> Arrays.stream(inspectItem.split("/")).map(Long::valueOf).collect(Collectors.toList())).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Long, DetectionMethod> detectionMethodMap = detectionMethodMapper.selectList(Wrappers.<DetectionMethod>lambdaQuery().eq(DetectionMethod::getIsEffect, IsEffectEnum.NORMAL.getCode()).in(DetectionMethod::getId, inspectItemIds)).stream().collect(Collectors.toMap(DetectionMethod::getId, Function.identity()));
        // 获取附件列表
        List<Long> equipmentIds = equipmentList.stream().map(Equipment::getId).collect(Collectors.toList());
        Map<Long, List<MaterialAttachment>> attachmentMap = materialAttachmentMapper.selectList(Wrappers.<MaterialAttachment>lambdaQuery().eq(MaterialAttachment::getIsEffect, IsEffectEnum.NORMAL.getCode()).in(MaterialAttachment::getMaterialId, equipmentIds)).stream().collect(Collectors.groupingBy(MaterialAttachment::getMaterialId));
        // 填充每个设备的拓展字段
        for (Equipment equipment : equipmentList) {
            if (ObjectUtil.isNull(equipment)) {
                continue;
            }
            // 填充仪器用途名称列表
            if (StrUtil.isNotBlank(equipment.getUsage())) {
                List<String> usageNameList = Arrays.stream(equipment.getUsage().split(",")).map(usage -> dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_USAGE.getCode() + "_" + usage, StrUtil.EMPTY)).collect(Collectors.toList());
                equipment.setUsageNameList(usageNameList);
            }
            // 填充仪器所属名称
            if (equipment.getOwnershipStatus() != null) {
                equipment.setOwnershipStatusName(dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_OWNERSHIP_STATUS.getCode() + "_" + equipment.getOwnershipStatus(), StrUtil.EMPTY));
            }
            // 填充状态名称
            if (equipment.getStatus() != null) {
                equipment.setStatusName(dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_STATUS.getCode() + "_" + equipment.getStatus(), StrUtil.EMPTY));
            }
            // 品牌名称暂时使用品牌字段本身
            if (StrUtil.isNotBlank(equipment.getBrand())) {
                equipment.setBrandName(dictMap.getOrDefault(DictDataTypeEnum.EQUIPMENT_BRAND.getCode() + "_" + equipment.getBrand(), StrUtil.EMPTY));
            }
            // 填充检测能力列表
            if (StrUtil.isNotBlank(equipment.getInspectItem())) {
                List<DetectionMethod> inspectItemList = Arrays.stream(equipment.getInspectItem().split("/")).map(Long::valueOf).map(detectionMethodMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                equipment.setInspectItemList(inspectItemList);
            }
            // 填充附件
            equipment.setAttachments(attachmentMap.getOrDefault(equipment.getId(), CollUtil.newArrayList()).stream().sorted(Comparator.comparing(MaterialAttachment::getCreateTime).reversed()).collect(Collectors.toList()));
        }
    }

    /**
     * 获取字典数据映射
     *
     * @param isMustEnable 是否必须启用
     * @return 字典数据映射 key: 字典类型_字典值, value: 字典标签
     */
    private Map<String, String> getDictDataMap(boolean isMustEnable) {
        // 查询所有有效的字典数据
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(isMustEnable, DictData::getStatus, EnableEnum.ENABLE.getCode());
        List<DictData> dictDataList = dictDataMapper.selectList(wrapper);
        // 构建父级字典映射 (字典值 -> ID)
        Map<String, Long> parentDictMap = dictDataList.stream().filter(dict -> dict.getParentId() == 0L).collect(Collectors.toMap(DictData::getDictValue, DictData::getId));
        // 构建子级字典映射 (父级字典值_子级字典值 -> 子级字典标签)
        return dictDataList.stream().filter(dict -> dict.getParentId() != 0L).filter(dict -> parentDictMap.containsValue(dict.getParentId())).collect(Collectors.toMap(dict -> {
            // 找到父级字典值
            String parentDictValue = parentDictMap.entrySet().stream().filter(entry -> entry.getValue().equals(dict.getParentId())).map(Map.Entry::getKey).findFirst().orElse(StrUtil.EMPTY);
            return parentDictValue + "_" + dict.getDictValue();
        }, DictData::getDictLabel, (existing, replacement) -> existing));
    }

    /**
     * 分析字典数据变更
     *
     * @param originalData      原始数据
     * @param newData           新数据
     * @param allDictDataMap    所有字典数据映射
     * @param enableDictDataMap 启用字典数据映射
     * @param dictDataTypeEnum  字典数据类型
     * @return 字典数据变更分析结果
     */
    private DataChangeAnalyzer.DataChangeResult<String> analyzeDictDataChange(String originalData, String newData, Map<String, String> allDictDataMap, Map<String, String> enableDictDataMap, DictDataTypeEnum dictDataTypeEnum) {
        // 初始化结果
        DataChangeAnalyzer.DataChangeResult<String> dictDataChangeResult = new DataChangeAnalyzer.DataChangeResult<>(CollUtil.newArrayList(), CollUtil.newArrayList(), CollUtil.newArrayList(), CollUtil.newArrayList(), CollUtil.newArrayList());
        // 比对分析结果
        DataChangeAnalyzer.DataChangeResult<String> changeResult = DataChangeAnalyzer.analyzeStringChange(originalData, newData);
        // 转换分析结果结果为字典数据
        if (CollUtil.isNotEmpty(changeResult.getAddedItems())) {
            changeResult.getAddedItems().forEach(added -> {
                String dictLabel = enableDictDataMap.get(dictDataTypeEnum.getCode() + "_" + added);
                // 校验新增的字典数据是否存在
                if (dictLabel == null) {
                    throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
                }
                dictDataChangeResult.getAddedItems().add(dictLabel);

            });
        }
        if (CollUtil.isNotEmpty(changeResult.getRemovedItems())) {
            changeResult.getRemovedItems().forEach(removed -> {
                String dictLabel = allDictDataMap.get(dictDataTypeEnum.getCode() + "_" + removed);
                // 校验删除的字典数据是否存在
                if (dictLabel == null) {
                    throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
                }
                dictDataChangeResult.getRemovedItems().add(dictLabel);
            });
        }
        if (CollUtil.isNotEmpty(changeResult.getRetainedItems())) {
            changeResult.getRetainedItems().forEach(retained -> {
                String dictLabel = allDictDataMap.get(dictDataTypeEnum.getCode() + "_" + retained);
                // 校验保留的字典数据是否存在
                if (dictLabel == null) {
                    throw new BusinessException(PcsResultCode.DICT_DATA_NOT_EXIST);
                }
                dictDataChangeResult.getRetainedItems().add(dictLabel);
            });
        }
        return dictDataChangeResult;
    }
}