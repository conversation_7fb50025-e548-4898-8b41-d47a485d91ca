package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.core.*;
import com.lanhu.lims.gateway.admin.diff.util.DiffUtil;
import com.lanhu.lims.gateway.admin.diff.vo.ChangeResult;
import com.lanhu.lims.gateway.admin.diff.vo.FileAttachmentChangeItem;
import com.lanhu.lims.gateway.admin.diff.vo.FileAttachmentChangeResult;
import com.lanhu.lims.gateway.admin.enums.*;
import com.lanhu.lims.gateway.admin.file.service.IFileService;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.StringUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 委托单服务
 * @author: huangzheng
 * @date: 2025/6/10 9:49
 */
@Service
public class EntrustOrderService {


    @Resource
    private EntrustOrderMapper entrustOrderMapper;


    @Resource
    private EntrustOrderAttachmentMapper entrustOrderAttachmentMapper;


    @Resource
    private SampleMapper sampleMapper;



    @Resource
    private InvoiceMapper invoiceMapper;


    @Resource
    private EntrustOrderAttachmentService entrustOrderAttachmentService;


    @Resource
    private InvoiceService invoiceService;




    @Resource
    private SampleService sampleService;


    @Resource
    private BusinessService businessService;


    @Resource
    private FileRecordMapper fileRecordMapper;


    @Resource
    private EntrustOrderService entrustOrderService;


    @Resource
    private IFileService fileService;


    @Resource
    private TemplateFileMapper templateFileMapper;



    /**
     * 委托单列表分页查询
     */
    @DS("slave_1")
    public PcsResult<Page<EntrustOrder>> listPage(EntrustOrderListPageForm form) {


        LambdaQueryWrapper<EntrustOrder> entrustOrderLambdaQueryWrapper = Wrappers.lambdaQuery();

        // 匹配委托单单号
        if (StringUtil.isNotBlank(form.getEntrustOrderNo())) {
            entrustOrderLambdaQueryWrapper.eq(EntrustOrder::getEntrustOrderNo, form.getEntrustOrderNo());
        }


        // 模糊匹配委托单位
        if(StringUtil.isNotBlank(form.getCustomerName())){
            entrustOrderLambdaQueryWrapper.like(EntrustOrder::getCustomerName,form.getCustomerName());
        }


        // 匹配委托单状态
        if(form.getStatus()!=null){

            entrustOrderLambdaQueryWrapper.eq(EntrustOrder::getStatus,form.getStatus());

        }else if(form.getGlobalStatus()!=null){
            // 不是进行中状态
            if (form.getGlobalStatus()!=EntrustOrderGlobalStatusEnum.RUNNING.getCode()) {
                entrustOrderLambdaQueryWrapper.eq(EntrustOrder::getStatus,form.getGlobalStatus());
            }else {
                // 进行中状态，非待提交，已终止，已完成状态
                entrustOrderLambdaQueryWrapper.notIn(EntrustOrder::getStatus,
                        Lists.newArrayList(
                                EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode(),
                                EntrustOrderStatusEnum.TERMINATED.getCode(),
                                EntrustOrderStatusEnum.COMPLETED.getCode()));
            }
        }


        entrustOrderLambdaQueryWrapper.eq(EntrustOrder::getIsEffect, IsEffectEnum.NORMAL.getCode());

        entrustOrderLambdaQueryWrapper.orderByDesc(EntrustOrder::getCreateTime);

        Page<EntrustOrder> entrustOrderPage = new Page<>(form.getPageIndex(), form.getPageSize());



        entrustOrderPage = entrustOrderMapper.selectPage(entrustOrderPage, entrustOrderLambdaQueryWrapper);


        entrustOrderPage.getRecords().forEach(entrustOrder -> {

            // 填充样本形式列表
            entrustOrder.setSampleFormList(sampleService.selectSampleFormListByEntrustOrderId(entrustOrder.getId()));

        });



        return Result.ok(entrustOrderPage);

    }






    /**
     * 录入委托单客户信息
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult<EntrustOrderAddCustomerVO> addCustomer(EntrustOrderCustomerAddForm form, LoginUser loginUser) {



        // 拷贝客户信息到委托单对象中
        EntrustOrder entrustOrder = null;


        // 检测委托单编号是否存在,如果是空，表明是第一次保存，就生成一个id和委托单编号，如果不是空，就使用传入的id和委托单编号
        if (form.getEntrustOrderId()==null) {
            entrustOrder = BeanUtil.copyProperties(form, EntrustOrder.class);
            // 生成委托单编号
            String entrustOrderNo  = SequenceUtil.getNextDynamicFormattedValue(
                    SequenceNameEnum.ENTRUST_ORDER_NO.getMonthlyNameGenerator(),
                    SequenceNameEnum.ENTRUST_ORDER_NO.getFormatter());

            // 填充委托单编号
            entrustOrder.setEntrustOrderNo(entrustOrderNo);

            // 填充id
            entrustOrder.setId(IdUtil.getSnowflakeNextId());

            // 填充创建人和修改人信息
            entrustOrder.setCreateBy(loginUser.getSysUser().getId());
            entrustOrder.setCreateName(loginUser.getSysUser().getRealName());
            entrustOrder.setCreateTime(new Date());

        }else {

            // 校验id的合法性
            EntrustOrder checkEntrustOrder = entrustOrderMapper.selectById(form.getEntrustOrderId());

            // 校验委托单是否存在
            if (checkEntrustOrder==null) {
                return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
            }

            entrustOrder = BeanUtil.copyProperties(form, EntrustOrder.class);

            // 不是第一次保存，就使用传入的id
            entrustOrder.setId(form.getEntrustOrderId());





        }


        if (form.getCustomerId()!=null) {
            // todo 查询客户表填充客户信息
        }




        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateName(loginUser.getSysUser().getRealName());
        entrustOrder.setUpdateTime(new Date());


        // 设置状态为待提交
        entrustOrder.setStatus(EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode());
        entrustOrder.setAuditStatus(AuditStatusEnum.PENDING.getCode());

        // 将客户信息插入或更新委托单主表
        entrustOrderMapper.insertOnDuplicateUpdateSelective(entrustOrder);


        // 给前端返回委托单id
        EntrustOrderAddCustomerVO entrustOrderAddCustomerVO = new EntrustOrderAddCustomerVO();
        entrustOrderAddCustomerVO.setEntrustOrderId(entrustOrder.getId());



        // 保存附件信息


        ArrayList<EntrustOrderAttachment> entrustOrderAttachments = new ArrayList<>();
        if (CollUtil.isEmpty(form.getAttachments())){
            // 没有附件 就可以直接返回了
            return Result.ok(entrustOrderAddCustomerVO);
        }
        for (EntrustOrderAttachmentAddForm attachmentAddForm : form.getAttachments()) {



            EntrustOrderAttachment entrustOrderAttachment = new EntrustOrderAttachment();

            // 附件id不为空，说明已经存在，不需要新增
            if (attachmentAddForm.getAttachmentId()!=null) {
                continue;
            }

            // 不存在 新增
            entrustOrderAttachment.setId(IdUtil.getSnowflakeNextId());
            FileRecord fileRecord = fileRecordMapper.selectById(attachmentAddForm.getFileId());

            if (fileRecord!=null) {

                entrustOrderAttachment.setAttachmentName(fileRecord.getAttachmentName());
                entrustOrderAttachment.setUrl(fileRecord.getAttachmentUrl());
                entrustOrderAttachment.setAttachmentSize(fileRecord.getAttachmentSize());
                entrustOrderAttachment.setAttachmentSuffix(fileRecord.getAttachmentSuffix());

            }



            entrustOrderAttachment.setFileId(attachmentAddForm.getFileId());



            entrustOrderAttachment.setEntrustOrderId(entrustOrder.getId());
            entrustOrderAttachment.setIsEffect(IsEffectEnum.NORMAL.getCode());

            entrustOrderAttachment.setCreateBy(loginUser.getSysUser().getId());
            entrustOrderAttachment.setCreateName(loginUser.getSysUser().getRealName());
            entrustOrderAttachment.setCreateTime(new Date());

            entrustOrderAttachment.setUpdateBy(loginUser.getSysUser().getId());
            entrustOrderAttachment.setUpdateName(loginUser.getSysUser().getRealName());
            entrustOrderAttachment.setUpdateTime(new Date());
            entrustOrderAttachments.add(entrustOrderAttachment);
        }

        // 只做插入不做修改
        entrustOrderAttachmentMapper.batchInsert(entrustOrderAttachments);


        return Result.ok(entrustOrderAddCustomerVO);
    }





    /**
     * 录入委托单样本信息
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult addSample(EntrustOrderSampleAddForm form, LoginUser loginUser) {


        Long entrustOrderId = form.getEntrustOrderId();
        EntrustOrder checkEntrustOrder = entrustOrderMapper.selectById(entrustOrderId);

        // 校验委托单是否存在
        if (checkEntrustOrder == null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }

        // 校验委托单状态是否为待提交
        if (checkEntrustOrder.getStatus()!=EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }

        EntrustOrder entrustOrder = new EntrustOrder();
        entrustOrder.setId(entrustOrderId);


        List<Sample> allSampleList = new ArrayList<>();

        // 先添加独立样本信息
        List<IndependentSampleAddForm> independentSampleForm = form.getIndependentSamples();


        if (CollUtil.isNotEmpty(form.getIndependentSamples())) {

            // 需要保存的独立样本信息
            ArrayList<Sample> independentSampleList = Lists.newArrayList();

            // 批量申请样本编号
            int size = independentSampleForm.size();
            List<String> sampleCodeList = SequenceUtil.getNextBatchDynamicFormattedValues(
                    SequenceNameEnum.SAMPLE_CODE.getMonthlyNameGenerator(),
                    SequenceNameEnum.SAMPLE_CODE.getFormatter(), size);

            int currentIndex = 0;

            // 组装样本信息
            for (IndependentSampleAddForm independentSampleAddForm : independentSampleForm) {


                Sample sample = null;


                // 新样本
                if (independentSampleAddForm.getId()==null){
                    sample = BeanUtil.copyProperties(independentSampleAddForm, Sample.class);
                    sample.setId(IdUtil.getSnowflakeNextId());
                    sample.setBarcode(sample.getId().toString());
                    sample.setEntrustOrderNo(checkEntrustOrder.getEntrustOrderNo());

                    sample.setSampleForm(SampleFormEnum.INDEPENDENT_SAMPLE.getCode());
                    sample.setStatus(SampleStatusEnum.PENDING.getCode());
                    sample.setIsEffect(IsEffectEnum.NORMAL.getCode());
                    sample.setEntrustOrderId(entrustOrderId);
                    sample.setCreateName(loginUser.getSysUser().getRealName());
                    sample.setCreateTime(new Date());
                    sample.setCreateBy(loginUser.getSysUser().getId());
                }else {
                    sample = sampleMapper.selectById(independentSampleAddForm.getId());
                    BeanUtil.copyProperties(independentSampleAddForm, sample);
                }

                // 所有的样本编号重新赋值，保证连续性
                sample.setSampleCode(sampleCodeList.get(currentIndex++));

                // 所有新的样本的parentId都为0，待办原始样本
                sample.setParentId(0L);

                sample.setUpdateBy(loginUser.getSysUser().getId());
                sample.setUpdateName(loginUser.getSysUser().getRealName());
                sample.setUpdateTime(new Date());


                independentSampleList.add(sample);

            }


//            // 批量新增或者更新样本信息
//            sampleMapper.batchInsertOrUpdate(independentSamples);

            allSampleList.addAll(independentSampleList);
        }



        // 添加96孔版样本信息
        List<PlateAddForm> plateAddFormList = form.getPlates();


        if (CollUtil.isNotEmpty(plateAddFormList)) {
            // 96孔板样本信息
            List<Sample> plateSampleList = Lists.newArrayList();
            for (PlateAddForm plateAddForm : plateAddFormList) {

                String plateNum = plateAddForm.getPlateNum();

//                // 校验96孔板编号是否存在
//                LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();
//                sampleLambdaQueryWrapper.select(Sample::getId);
//                sampleLambdaQueryWrapper.eq(Sample::getPlateNum, plateNum);
//                sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, entrustOrderId);
//                sampleLambdaQueryWrapper.eq(Sample::getStatus, SampleStatusEnum.PENDING.getCode());
//
//                Long counts = sampleMapper.selectCount(sampleLambdaQueryWrapper);

//                // 若存在就先删除 （物理删除）
//                if (counts > 0) {
//                    LambdaUpdateWrapper<Sample> deleteWrapper = Wrappers.lambdaUpdate();
//                    deleteWrapper.eq(Sample::getPlateNum, plateNum);
//                    deleteWrapper.eq(Sample::getEntrustOrderId, entrustOrderId);
//                    sampleLambdaQueryWrapper.eq(Sample::getStatus, SampleStatusEnum.PENDING.getCode());
//                    sampleMapper.delete(deleteWrapper);
//                }


                List<PlateSampleAddForm> plateSampleFormList = plateAddForm.getPlateSamples();

                if (CollUtil.isNotEmpty(plateSampleFormList)) {

                    // 批量申请样本编号
                    int size = plateSampleFormList.size();
                    List<String> sampleCodeList = SequenceUtil.getNextBatchDynamicFormattedValues(
                            SequenceNameEnum.SAMPLE_CODE.getMonthlyNameGenerator(),
                            SequenceNameEnum.SAMPLE_CODE.getFormatter(), size);

                    int currentIndex = 0;

                    // 一个96孔版的样本共用同一个条形码
                    String barCode = IdUtil.getSnowflakeNextIdStr();
                    // 组装96孔板样本信息
                    for (PlateSampleAddForm plateSampleAddForm : plateSampleFormList) {

                        Sample sample= null;


                        if (plateSampleAddForm.getId()==null){
                            // 说明是96孔板新增的样本
                            sample = BeanUtil.copyProperties(plateSampleAddForm, Sample.class);

                            sample.setId(IdUtil.getSnowflakeNextId());
                            sample.setPlateNum(plateNum);
                            sample.setEntrustOrderId(entrustOrderId);
                            sample.setIsEffect(IsEffectEnum.NORMAL.getCode());
                            sample.setEntrustOrderNo(checkEntrustOrder.getEntrustOrderNo());
                            sample.setSampleForm(SampleFormEnum.PLATE_96.getCode());
                            sample.setBarcode(barCode);
                            sample.setStatus(SampleStatusEnum.PENDING.getCode());

                            sample.setCreateBy(loginUser.getSysUser().getId());
                            sample.setCreateName(loginUser.getSysUser().getRealName());
                            sample.setCreateTime(new Date());
                        }
                        else {
                            sample = sampleMapper.selectById(plateSampleAddForm.getId());
                            // 拷贝修改的信息
                            BeanUtil.copyProperties(plateSampleAddForm, sample);
                        }

                        sample.setSampleCode(sampleCodeList.get(currentIndex++));


                        // 所有新的样本的parentId都为0，待办原始样本
                        sample.setParentId(0L);

                        sample.setUpdateBy(loginUser.getSysUser().getId());
                        sample.setUpdateName(loginUser.getSysUser().getRealName());
                        sample.setUpdateTime(new Date());

                        plateSampleList.add(sample);

                    }

                }


            }

            allSampleList.addAll(plateSampleList);

        }

        sampleMapper.batchInsertOrUpdate(allSampleList);





        // 统计委托单的检测项目
        Set<String> inspectItemSet = new HashSet<>();
        for (Sample sample : allSampleList) {

            // 每个样本的检测项目
            String[] split = sample.getInspectItem().split(ProjectConstant.LEFT_SLASH);

            inspectItemSet.addAll(Arrays.asList(split));

        }


        //  根据样本检测的检测项目计算委托单总金额
        HashMap<String, Double> InspectItemPriceMap = new HashMap<>();
        // todo 查检测项目表 初始化检测项目价格InspectItemPriceMap 根据项目名称获取价格



        // 计算计算标准价格
        BigDecimal inspectItemPrice = BigDecimal.ZERO;


        for (Sample sample : allSampleList) {
            // 计算委托单的检测项目
            String[] split = sample.getInspectItem().split(ProjectConstant.LEFT_SLASH);
            for (String s : split) {
                Double price = InspectItemPriceMap.getOrDefault(s,0.);
                inspectItemPrice = inspectItemPrice.add(new BigDecimal(price));
            }
        }

        // 设置委托单的基准价格
        entrustOrder.setBenchmarkAmount(inspectItemPrice);


        // 拼接委托单的检测项目
        if (!inspectItemSet.isEmpty()) {
            String inspectItems = String.join(ProjectConstant.LEFT_SLASH, inspectItemSet);
            entrustOrder.setInspectItem(inspectItems);
        }

        // 更新委托单主表
        entrustOrder.setSampleAmount(allSampleList.size());
        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateName(loginUser.getSysUser().getRealName());
        entrustOrder.setUpdateTime(new Date());
        entrustOrderMapper.updateById(entrustOrder);


        return Result.ok();

    }







    /**
     * 录入委托单其他信息
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult addOther(EntrustOrderOtherAddForm form, LoginUser loginUser) {


        Long entrustOrderId = form.getEntrustOrderId();
        EntrustOrder checkEntrustOrder = entrustOrderMapper.selectById(entrustOrderId);

        // 校验委托单是否存在
        if (checkEntrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }

        // 校验委托单状态是否为待提交
        if (checkEntrustOrder.getStatus()!=EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }

        // 拷贝委托信息和报告信息到委托单对象中
        EntrustOrder entrustOrder = BeanUtil.copyProperties(form, EntrustOrder.class);

        // 填充委托单id
        entrustOrder.setId(entrustOrderId);

        entrustOrder.setPlanCompleteTime(DateUtil.parse(form.getPlanCompleteDate(), ProjectConstant.DATE_TIME_FORMAT));

        // 添加修改信息
        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateName(loginUser.getSysUser().getRealName());
        entrustOrder.setUpdateTime(new Date());

        // 更新主表
        entrustOrderMapper.updateById(entrustOrder);



        // 保存发票信息

        Invoice invoice = BeanUtil.copyProperties(form, Invoice.class);


        if (form.getInvoiceId()==null) {

            invoice.setId(IdUtil.getSnowflakeNextId());
            invoice.setEntrustOrderId(entrustOrderId);
            invoice.setEntrustOrderNo(checkEntrustOrder.getEntrustOrderNo());
            invoice.setIsEffect(IsEffectEnum.NORMAL.getCode());
            invoice.setCreateBy(loginUser.getSysUser().getId());
            invoice.setCreateName(loginUser.getSysUser().getRealName());
            invoice.setCreateTime(new Date());

        }else {
            invoice.setId(form.getInvoiceId());
        }
        invoice.setUpdateBy(loginUser.getSysUser().getId());
        invoice.setUpdateName(loginUser.getSysUser().getRealName());
        invoice.setUpdateTime(new Date());

        invoiceMapper.insertOnDuplicateUpdateSelective(invoice);


        return Result.ok();
    }















    /**
     * 提交委托单，发起委托单审批流程
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult commit(EntrustOrderCommitForm form, LoginUser loginUser) {



        Long entrustOrderId = form.getEntrustOrderId();
        EntrustOrder checkEntrustOrder = entrustOrderMapper.selectById(entrustOrderId);

        // 校验委托单是否存在
        if (checkEntrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }

        // 校验委托单状态是否为待提交
        if (checkEntrustOrder.getStatus()!=EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }


        // 检测委托单信息完整性，是否可以提交
        PcsResult pcsResult = entrustOrderService.checkEntrustOrderInfoIsComplete(entrustOrderId);

        // 完整性检测失败
        if (pcsResult.getCode()!=PcsResultCode.SUCCESS.getCode()) {

            return pcsResult;

        }

        // 检测成功 发起流程
        Long instanceId = businessService.submitAddAudit(FlowBusinessCategoryEnum.ENTRUST_ORDER_INSERT_BUSINESS.getCode(), entrustOrderId);


        EntrustOrder entrustOrder = new EntrustOrder();
        entrustOrder.setId(entrustOrderId);

        // 更新委托单的流程实例id
        entrustOrder.setFlowInstanceId(instanceId);
        entrustOrder.setStatus(EntrustOrderStatusEnum.AUDITING.getCode());
        entrustOrder.setAuditStatus(AuditStatusEnum.PROCESSING.getCode());

        entrustOrder.setCreateBy(loginUser.getSysUser().getId());
        entrustOrder.setCreateName(loginUser.getSysUser().getRealName());
        entrustOrder.setCreateTime(new Date());

        entrustOrderMapper.updateById(entrustOrder);


        return Result.ok();
    }






    /**
     * 校验委托单信息完整性，是否可以提交
     */
    @DS("slave_1")
    public PcsResult checkEntrustOrderInfoIsComplete(Long entrustOrderId) {


        EntrustOrder entrustOrder = entrustOrderMapper.selectById(entrustOrderId);


        // 检测委托单必填信息是否完整

        // 1. 检测客户信息是否完整
        if(StringUtil.isBlank(entrustOrder.getCustomerName())
                ||StringUtil.isBlank(entrustOrder.getContactName())){

            return Result.error(PcsResultCode.ENTRUST_ORDER_CUSTOMER_INFO_INCOMPLETE);

        }

        // 2. 检测样本信息是否完整

        List<Sample> sampleList = sampleService.selectListByEntrustOrderId(entrustOrderId);

        // 无样本
        if (CollUtil.isEmpty(sampleList)) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE);
        }

        // 检测样本信息是否完整
        for (Sample sample : sampleList) {
            if (StringUtil.isBlank(sample.getSampleName())
                    || StringUtil.isBlank(sample.getInspectItem())
                    || StringUtil.isBlank(sample.getSpeciesName())
                    || StringUtil.isBlank(sample.getSampleType())){
                return Result.error(PcsResultCode.ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE);
            }
            // 96孔板要另外判断位置和板号
            if(sample.getSampleForm()==SampleFormEnum.PLATE_96.getCode()){
                if (StringUtil.isBlank(sample.getPositionNum())
                        || StringUtil.isBlank(sample.getPlateNum())){
                    return Result.error(PcsResultCode.ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE);
                }
            }
        }


        // 3. 检测其他信息是否完整

        // 检测委托信息
        if (entrustOrder.getBenchmarkAmount()==null
                || entrustOrder.getDeliveryMode() == null
                || entrustOrder.getPlanCompleteDays() == null
                || entrustOrder.getPlanCompleteTime() == null){

            return Result.error(PcsResultCode.ENTRUST_ORDER_ENTRUST_INFO_INCOMPLETE);

        }

        // 检测报告信息
        if (entrustOrder.getReportAmount() == null
                ||entrustOrder.getReportFormat() == null){
            return Result.error(PcsResultCode.ENTRUST_ORDER_REPORT_INFO_INCOMPLETE);
        }


        // 检测发票信息


//        LambdaQueryWrapper<Invoice> invoiceQueryWrapper = Wrappers.lambdaQuery();
//        invoiceQueryWrapper.eq(Invoice::getEntrustOrderId, entrustOrderId);
//        invoiceQueryWrapper.eq(Invoice::getIsEffect, IsEffectEnum.NORMAL.getCode());
//        List<Invoice> invoices = invoiceMapper.selectList(invoiceQueryWrapper);

        Invoice invoice = invoiceService.selectByEntrustOrderId(entrustOrderId);

        if (invoice==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE);
        }


        if (invoice.getInvoiceType()==null
                    || StringUtil.isBlank(invoice.getInvoiceTitle())
                    || StringUtil.isBlank(invoice.getTaxNumber())){
            return Result.error(PcsResultCode.ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE);
        }

        // 增值税专业发票要额外检查判断
        if (invoice.getInvoiceType()== InvoiceTypeEnum.SPECIAL_VAT_INVOICE.getCode()){

            if (StringUtil.isBlank(invoice.getDepositBankName())
                    || StringUtil.isBlank(invoice.getDepositAccount())){
                    return Result.error(PcsResultCode.ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE);
            }


        }


        return Result.ok();

    }




    /**
     * 查询委托单新增页面的客户信息的内容
     */
    @DS("slave_1")
    public PcsResult<EntrustOrderCustomerDetailVO> customerDetail(EntrustOrderCustomerDetailForm form) {


        Long entrustOrderId = form.getEntrustOrderId();

        EntrustOrder entrustOrder = entrustOrderMapper.selectById(entrustOrderId);
        // 校验委托单是否存在
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }

        EntrustOrderCustomerDetailVO entrustOrderCustomerDetailVO = BeanUtil.copyProperties(entrustOrder, EntrustOrderCustomerDetailVO.class);


        // 查询委托单的附件信息
        List<EntrustOrderAttachment> attachmentList = entrustOrderAttachmentService.selectListByEntrustOrderId(entrustOrderId);



        List<EntrustOrderAttachmentVO> attachments = attachmentList.stream().map(entrustOrderAttachment -> BeanUtil.copyProperties(entrustOrderAttachment, EntrustOrderAttachmentVO.class))
                .collect(Collectors.toList());

        entrustOrderCustomerDetailVO.setAttachments(attachments);


        return Result.ok(entrustOrderCustomerDetailVO);


    }




    /**
     * 查询委托单新增页面的样本信息的内容
     */
    @DS("slave_1")
    public PcsResult<EntrustOrderSampleDetailVO> sampleDetail(EntrustOrderSampleDetailForm form) {


        Long entrustOrderId = form.getEntrustOrderId();

        EntrustOrder entrustOrder = entrustOrderMapper.selectById(entrustOrderId);


        // 校验委托单是否存在
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }


        // 查询委托单的样本信息
        List<Sample> sampleList = sampleService.selectListByEntrustOrderId(entrustOrderId);


        if (CollUtil.isEmpty(sampleList)) {
            return Result.ok(new EntrustOrderSampleDetailVO(Collections.emptyList(),0,Collections.emptyList(),Collections.emptyList()));
        }

        // 返回数据的VO
        EntrustOrderSampleDetailVO entrustOrderSampleDetailVO = new EntrustOrderSampleDetailVO();

        // 总样本数
        entrustOrderSampleDetailVO.setSampleNum(sampleList.size());

        // 样本形式
        List<Integer> sampleFormList = Lists.newArrayList();

        // 区分独立样本和96孔版样本
        List<EntrustOrderIndependentSampleVO> independentSampleList = Lists.newArrayList();
        List<EntrustOrderPlateSampleVO> plateSampleList = Lists.newArrayList();

        for (Sample sample : sampleList) {

            if (sample.getSampleForm()==SampleFormEnum.PLATE_96.getCode()){

                EntrustOrderPlateSampleVO entrustOrderPlateSampleVO = BeanUtil.copyProperties(sample, EntrustOrderPlateSampleVO.class);
                plateSampleList.add(entrustOrderPlateSampleVO);

            } else {

                // 非96孔板全当做独立样本
                EntrustOrderIndependentSampleVO entrustOrderIndependentSampleVO = BeanUtil.copyProperties(sample, EntrustOrderIndependentSampleVO.class);
                independentSampleList.add(entrustOrderIndependentSampleVO);
            }
        }

        // 组装独立样本
        if (CollUtil.isNotEmpty(independentSampleList)) {
            sampleFormList.add(SampleFormEnum.INDEPENDENT_SAMPLE.getCode());
            entrustOrderSampleDetailVO.setIndependentSamples(independentSampleList);
        }



        ArrayList<EntrustOrderPlateVO> plateList = Lists.newArrayList();
        // 组装96孔版样本
        if(CollUtil.isNotEmpty(plateSampleList)){

            // 96孔版样本按照板号分组
            Map<String, List<EntrustOrderPlateSampleVO>> platesMap = plateSampleList.stream()
                    .collect(Collectors.groupingBy(EntrustOrderPlateSampleVO::getPlateNum));


            for (Map.Entry<String, List<EntrustOrderPlateSampleVO>> entry : platesMap.entrySet()) {

                EntrustOrderPlateVO entrustOrderPlateVO = new EntrustOrderPlateVO();
                entrustOrderPlateVO.setPlateNum(entry.getKey());
                entrustOrderPlateVO.setPlateSampleList(entry.getValue());
                entrustOrderPlateVO.setSampleNum(entry.getValue().size());
                plateList.add(entrustOrderPlateVO);
            }

            sampleFormList.add(SampleFormEnum.PLATE_96.getCode());

        }

        entrustOrderSampleDetailVO.setPlates(plateList);

        entrustOrderSampleDetailVO.setSampleFormList(sampleFormList);


        return Result.ok(entrustOrderSampleDetailVO);



    }






    /**
     * 查询委托单新增页面的其他信息的内容
     */
    @DS("slave_1")
    public PcsResult<EntrustOrderOtherDetailVO> otherDetail(EntrustOrderOtherDetailForm form) {


        Long entrustOrderId = form.getEntrustOrderId();

        EntrustOrder entrustOrder = entrustOrderMapper.selectById(entrustOrderId);


        // 校验委托单是否存在
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }


        // 组装基本信息
        EntrustOrderOtherDetailVO entrustOrderOtherDetailVO = BeanUtil.copyProperties(entrustOrder, EntrustOrderOtherDetailVO.class);



        // 组装发票信息
        Invoice invoice = invoiceService.selectByEntrustOrderId(entrustOrderId);

        if (invoice!=null) {

            BeanUtil.copyProperties(invoice, entrustOrderOtherDetailVO);

            entrustOrderOtherDetailVO.setInvoiceId(invoice.getId());
        }

        entrustOrderOtherDetailVO.setPlanCompleteDate(DateUtil.format(entrustOrder.getPlanCompleteTime(), ProjectConstant.DATE_TIME_FORMAT));
        entrustOrderOtherDetailVO.setUpdateTime(DateUtil.format(entrustOrder.getUpdateTime(), ProjectConstant.DATE_TIME_FORMAT));
        entrustOrderOtherDetailVO.setCreateTime(DateUtil.format(entrustOrder.getCreateTime(), ProjectConstant.DATE_TIME_FORMAT));


        return Result.ok(entrustOrderOtherDetailVO);
    }






    /**
     * 查询委托单详情页面顶栏的内容
     */
    @DS("slave_1")
    public PcsResult<EntrustOrderBannerDetailVO> bannerDetail(EntrustOrderBannerDetailForm form) {

        Long entrustOrderId = form.getEntrustOrderId();
        EntrustOrder entrustOrder = entrustOrderMapper.selectById(entrustOrderId);


        // 校验委托单是否存在
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }


        EntrustOrderBannerDetailVO entrustOrderBannerDetailVO = new EntrustOrderBannerDetailVO();

        // 委托单相关信息组装
        entrustOrderBannerDetailVO.setStatus(entrustOrder.getStatus());
        entrustOrderBannerDetailVO.setEntrustOrderNo(entrustOrder.getEntrustOrderNo());

        // 全局状态，除了已完成和已终止，待提交，审核中，其余 全部为进行中状态
        if (entrustOrder.getStatus()==EntrustOrderStatusEnum.COMPLETED.getCode()) {

            entrustOrderBannerDetailVO.setGlobalStatus(EntrustOrderGlobalStatusEnum.COMPLETED.getCode());
            entrustOrderBannerDetailVO.setPlanCompleteDate(DateUtil.format(entrustOrder.getPlanCompleteTime(), ProjectConstant.DATE_TIME_FORMAT));
            entrustOrderBannerDetailVO.setCompleteDate(DateUtil.format(entrustOrder.getCompleteTime(), ProjectConstant.DATE_TIME_FORMAT));
            entrustOrderBannerDetailVO.setInspectItems(entrustOrder.getInspectItem().replace(ProjectConstant.LEFT_SLASH,ProjectConstant.PAUSE));
            // todo 检测员工信息

        }else if (entrustOrderBannerDetailVO.getStatus()==EntrustOrderStatusEnum.TERMINATED.getCode()){

            entrustOrderBannerDetailVO.setGlobalStatus(EntrustOrderGlobalStatusEnum.TERMINATED.getCode());
            entrustOrderBannerDetailVO.setTerminateReason(entrustOrder.getCloseReason());

        }else if (entrustOrderBannerDetailVO.getStatus()==EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()) {

            entrustOrderBannerDetailVO.setGlobalStatus(EntrustOrderGlobalStatusEnum.PENDING_SUBMISSION.getCode());

        } else if (entrustOrderBannerDetailVO.getStatus()==EntrustOrderStatusEnum.AUDITING.getCode()) {

            entrustOrderBannerDetailVO.setPlanCompleteDate(DateUtil.format(entrustOrder.getPlanCompleteTime(), ProjectConstant.DATE_TIME_FORMAT));
            entrustOrderBannerDetailVO.setGlobalStatus(EntrustOrderGlobalStatusEnum.AUDITING.getCode());
            entrustOrderBannerDetailVO.setInspectItems(entrustOrder.getInspectItem().replace(ProjectConstant.LEFT_SLASH,ProjectConstant.PAUSE));


        } else {
            // 其余 全部为进行中状态
            entrustOrderBannerDetailVO.setGlobalStatus(EntrustOrderGlobalStatusEnum.RUNNING.getCode());
            entrustOrderBannerDetailVO.setPlanCompleteDate(DateUtil.format(entrustOrder.getPlanCompleteTime(), ProjectConstant.DATE_TIME_FORMAT));
            entrustOrderBannerDetailVO.setInspectItems(entrustOrder.getInspectItem().replace(ProjectConstant.LEFT_SLASH, ProjectConstant.PAUSE));
            // todo 检测员工信息


        }



        return Result.ok(entrustOrderBannerDetailVO);



    }




    /**
     * 委托单编辑申请
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult applyEdit(EntrustOrderEditApplyForm form, LoginUser loginUser) {

        // 校验委托单是否存在
        EntrustOrder originEntrustOrder = entrustOrderMapper.selectById(form.getEntrustOrderId());
        if (originEntrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }


        // 检测状态是否可以编辑  审核中，已完成，已终止 不可以提交编辑申请
        if (originEntrustOrder.getStatus()==EntrustOrderStatusEnum.AUDITING.getCode()
        || originEntrustOrder.getStatus()==EntrustOrderStatusEnum.COMPLETED.getCode()
        || originEntrustOrder.getStatus()==EntrustOrderStatusEnum.TERMINATED.getCode()
        || originEntrustOrder.getAuditStatus()==AuditStatusEnum.PROCESSING.getCode() ){

            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);

        }

        // 可以申请提交编辑
        

        // 比对申请的信息和原信息是否有变化，如果有变化，记录变化的信息

        // 比对委托单的客户信息
        EntrustOrder newEntrustOrder = BeanUtil.copyProperties(originEntrustOrder, EntrustOrder.class);

        BeanUtil.copyProperties(form, newEntrustOrder);

        ChangeResult customerChangeResult = DiffUtil.compare(newEntrustOrder, originEntrustOrder);


        // 比对委托单的附件信息
        List<EntrustOrderAttachment> originAttachments = entrustOrderAttachmentService.selectListByEntrustOrderId(form.getEntrustOrderId());

        List<EntrustOrderAttachmentAddForm> newAttachments = form.getAttachments();

        // todo 比对 originAttachments 和 newAttachments 附件信息是否有变化，如果有变化，记录变化的信息

        FileAttachmentChangeResult fileAttachmentChangeResult = new FileAttachmentChangeResult();

        List<FileAttachmentChangeItem> fileAttachmentChangeItems = Lists.newArrayList();






        // 审核通过后需要添加的文件id
        List<Long> addFileIds = Lists.newArrayList();

        // 审核通过后需要删除的附件id
        List<Long> delAttachmentIds = Lists.newArrayList();

        if (CollUtil.isNotEmpty(newAttachments)) {
            newAttachments.forEach(attachment -> {

                FileRecord fileRecord = fileRecordMapper.selectById(attachment.getFileId());
                FileAttachmentChangeItem item = new FileAttachmentChangeItem();
                if (attachment.getAttachmentId()==null){
                    addFileIds.add(attachment.getFileId());

                }else {
                    delAttachmentIds.add(attachment.getAttachmentId());
//                    item.setAttachmentId(attachment.getAttachmentId().toString());
                    item.setAttachmentId("123");
                }
//                item.setFileId(attachment.getFileId().toString());
//                item.setAttachmentName(fileRecord.getAttachmentName());

                item.setFileId(attachment.getFileId().toString());
                item.setAttachmentName("wenjian.pdf");
                fileAttachmentChangeItems.add(item);

            });


        }
        fileAttachmentChangeResult.setItemList(fileAttachmentChangeItems);



        // 附件变化信息
        ChangeResult attachmentChangeResult = DiffUtil.compare(fileAttachmentChangeResult);



        // 比对委托单的发票信息
        Invoice originInvoice = invoiceService.selectByEntrustOrderId(form.getEntrustOrderId());

        Invoice newInvoice = BeanUtil.copyProperties(form, Invoice.class);


        ChangeResult invoiceChangeResult = DiffUtil.compare(originInvoice, newInvoice);






//        // 将委托单的变化信息存入业务表中，用于后续的流程处理
//        BusinessFlowApplication businessFlowApplication = new BusinessFlowApplication();
//
//        // 编辑差异信息保存
//        // businessFlowApplication.setBusInfo();
//        businessFlowApplication.setSourceBusinessId(originEntrustOrder.getId());
//        businessFlowApplication.setAuditType(BusinessAuditTypeEnum.DATA_EDIT_REVIEW.getCode());



        // todo 发起流程

        Long instanceId = businessService.submitEditAudit(
                FlowBusinessCategoryEnum.ENTRUST_ORDER_EDIT_BUSINESS.getCode(),
                originEntrustOrder.getId(),
                new EntrustOrderDetailVO(originEntrustOrder, originInvoice, originAttachments,null,null),
                new EntrustOrderDetailVO(newEntrustOrder, newInvoice, null,addFileIds,delAttachmentIds),
                new EntrustOrderChangeVO(customerChangeResult, attachmentChangeResult, invoiceChangeResult));


        EntrustOrder entrustOrder = new EntrustOrder();

        // 更新委托单主表审批状态
        entrustOrder.setId(form.getEntrustOrderId());
        entrustOrder.setAuditStatus(AuditStatusEnum.PROCESSING.getCode());
        entrustOrder.setFlowInstanceId(instanceId);
        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateTime(new Date());
        entrustOrder.setUpdateName(loginUser.getRealName());

        entrustOrderMapper.updateById(originEntrustOrder);

        return Result.ok();

    }







    /**
     * 委托单终止
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult termination(EntrustOrderTerminationForm form, LoginUser loginUser) {

        // 校验委托单是否存在
        EntrustOrder entrustOrder = entrustOrderMapper.selectById(form.getEntrustOrderId());
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }


        // 待提交，已完成，审核中，或者已终止状态不可终止
        if (entrustOrder.getStatus()==EntrustOrderStatusEnum.COMPLETED.getCode()
                || entrustOrder.getStatus()==EntrustOrderStatusEnum.TERMINATED.getCode()) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }


        // 可以终止 发起流程

        Long instanceId = businessService.submitBusinessTerminationAudit(
                FlowBusinessCategoryEnum.ENTRUST_ORDER_TERMINATION_BUSINESS.getCode(),
                form.getEntrustOrderId(),
                form.getTerminationReason());

        // 回填instanceId
        entrustOrder.setFlowInstanceId(instanceId);
        entrustOrder.setAuditStatus(AuditStatusEnum.PROCESSING.getCode());
        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateTime(new Date());
        entrustOrder.setUpdateName(loginUser.getRealName());
        entrustOrderMapper.updateById(entrustOrder);

        return Result.ok();


    }



    /**
     * 委托单完成
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult complete(EntrustOrderCompleteForm form, LoginUser loginUser) {


        // 校验委托单是否存在
        EntrustOrder entrustOrder = entrustOrderMapper.selectById(form.getEntrustOrderId());
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }


        // 只有状态为报告生成中状态才能完成
        if (entrustOrder.getStatus()!=EntrustOrderStatusEnum.REPORT_GENERATION.getCode()) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }

        // 可以完成
        // 主表状态更新
        entrustOrder.setStatus(EntrustOrderStatusEnum.COMPLETED.getCode());
        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateTime(new Date());
        entrustOrder.setUpdateName(loginUser.getRealName());
        entrustOrderMapper.updateById(entrustOrder);


        // todo 同步更新样本，任务, 报告等状态






        return Result.ok();
    }



    /**
     * 委托单删除
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult del(EntrustOrderDelForm form, LoginUser loginUser) {


        // 校验委托单是否存在
        EntrustOrder entrustOrder = entrustOrderMapper.selectById(form.getEntrustOrderId());
        if (entrustOrder==null) {
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }

        // 判断是否可以删除, 只有待提交状态可以删除
        if (entrustOrder.getStatus()!=EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()){
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }

        entrustOrder.setIsEffect(IsEffectEnum.DELETE.getCode());
        entrustOrder.setUpdateBy(loginUser.getSysUser().getId());
        entrustOrder.setUpdateTime(new Date());
        entrustOrder.setUpdateName(loginUser.getRealName());
        entrustOrderMapper.updateById(entrustOrder);


        // 如已导入样本，同步删除样本信息
        sampleService.delByEntrustOrderId(form.getEntrustOrderId(), loginUser);

        // 如已导入发票，同步删除发票信息

        invoiceService.delByEntrustOrderId(form.getEntrustOrderId(), loginUser);


        // 如已导入附件，同步删除附件信息
        entrustOrderAttachmentService.delByEntrustOrderId(form.getEntrustOrderId(), loginUser);

        return Result.ok();






    }




    /**
     *
     *  预览附件内容
     *
     */
    @DS("slave_1")
    public PcsResult<String> previewAttachment(EntrustOrderAttachmentPreviewForm form) {

        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileId());

        if (fileRecord==null) {
            return Result.error(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }

        String filePath = fileRecord.getAttachmentUrl();

        return  fileService.encodeFile(filePath);


    }




    /**
     *
     *  下载附件
     *
     */
    @DS("slave_1")
    public void downloadAttachment(EntrustOrderAttachmentDownloadForm form, HttpServletResponse response) {

        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileId());
        if (fileRecord==null) {
            throw new BusinessException(PcsResultCode.FILE_RECORD_NOT_EXIST);
        }


        fileService.fileDownload(fileRecord.getAttachmentUrl(), fileRecord.getAttachmentName(), response);


    }




    /**
     *
     *  下载样本导入模板文件
     *
     */
    @DS("slave_1")
    public void downloadSampleImportTemplate(HttpServletResponse response) {


        LambdaQueryWrapper<TemplateFile> templateFileLambdaQueryWrapper = Wrappers.lambdaQuery();


        templateFileLambdaQueryWrapper.eq(TemplateFile::getTemplateType, TemplateFileTypeEnum.SAMPLE_IMPORT_TEMPLATE.getCode());

        templateFileLambdaQueryWrapper.eq(TemplateFile::getStatus, EnableEnum.ENABLE.getCode());

        templateFileLambdaQueryWrapper.eq(TemplateFile::getIsEffect, IsEffectEnum.NORMAL.getCode());


        TemplateFile templateFile = templateFileMapper.selectOne(templateFileLambdaQueryWrapper);

        if (templateFile==null) {
            throw new BusinessException(PcsResultCode.TEMPLATE_FILE_NOT_EXIST);
        }

        fileService.fileDownload(templateFile.getAttachmentUrl(), templateFile.getTemplateName(), response);



    }
}
