package com.lanhu.lims.gateway.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 模板文件类型枚举
 * @author: huangzheng
 * @date: 2025/6/17 13:35
 */

@Getter
@AllArgsConstructor
public enum TemplateFileTypeEnum {


    /**
     * 全局状态状态:(0： 报告模板，1: 原始记录模板，2：委托单模板，3： 分包合同模板， 4：流程文件模板，5：样本导入模板）
     */

    REPORT_TEMPLATE(0, "报告模板"),

    ORIGINAL_RECORD_TEMPLATE(1, "原始记录模板"),

    ENTRUST_ORDER_TEMPLATE(2, "委托单模板"),

    SUB_CONTRACT_TEMPLATE(3, "分包合同模板"),

    PROCESS_FILE_TEMPLATE(4, "流程文件模板"),

    SAMPLE_IMPORT_TEMPLATE(5, "样本导入模板"),
    ;







    private final int code;

    private final String msg;

    public static TemplateFileTypeEnum convert(int code) {
        for (TemplateFileTypeEnum value : TemplateFileTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return TemplateFileTypeEnum.REPORT_TEMPLATE;
    }


}
