package com.lanhu.lims.gateway.admin.vo.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 样本形式和样本数量出参
 * @author: huangzheng
 * @date: 2025/6/16 14:51
 */

@Data
@ApiModel(value = "样本形式和样本数量出参", description = "样本形式和样本数量出参")
public class SampleFormAndCountVO {






    @ApiModelProperty(value = "样本形式列表")
    private List<Integer> sampleFormList;

    @ApiModelProperty(value = "样本数量")
    private Integer sampleCount;


    @ApiModelProperty(value = "样本入库日期")
    private String storeTime;




}
