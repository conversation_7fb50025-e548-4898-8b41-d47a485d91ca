package com.lanhu.lims.gateway.admin.core;

import lombok.Getter;


@Getter
public enum PcsResultCode {
    /**
     * 0-成功
     * 1-90 基础业务错误信息
     * 90-200 用户相关
     * 200-300首页相关
     *
     * sys enum config
     */

    SUCCESS(0, "SUCCESS"),
    ERROR_OPERATE(1, "ERROR_OPERATE"),

    //token相关
    NOT_TOKEN(-1,"NOT_TOKEN"),
    INVALID_TOKEN(-2,"INVALID_TOKEN"),
    TOKEN_TIMEOUT(-3,"TOKEN_TIMEOUT"),
    BE_REPLACED(-4,"BE_REPLACED"),
    <PERSON><PERSON><PERSON>_OUT(-5,"KICK_OUT"),
    TOKEN_FREEZE(-6,"TOKEN_FREEZE"),
    NO_PREFIX(-7,"NO_PREFIX"),

    //验证码相关
    CAPTCHA_NOT_EXIST(-10, "CAPTCHA_NOT_EXIST"),

    //权限相关
    NO_PERMISSION(-20,"NO_PERMISSION"),
    NO_ROLE(-21,"NO_ROLE"),

    //数据验证
    NOT_FOUND_HANDLER(2, "NOT_FOUND_HANDLER"),
    METHOD_NOT_SUPPORT(3, "METHOD_NOT_SUPPORT"),
    MEDIA_TYPE_NOT_SUPPORT(4, "MEDIA_TYPE_NOT_SUPPORT"),
    PARAM_LOSE(5, "PARAM_LOSE"),
    PARAM_TYPE_NOT_SUPPORT(6, "PARAM_TYPE_NOT_SUPPORT"),


    //文件上传
    FILE_UPLOAD_ERROR(11, "FILE_UPLOAD_ERROR"),
    UPLOAD_FILE_CONFIG_ISNULL_ERROR(12, "UPLOAD_FILE_CONFIG_ISNULL_ERROR"),
    UPLOAD_OVER_SIZE(13, "UPLOAD_OVER_SIZE"),
    UPLOAD_FILE_SUF_ERROR(14, "UPLOAD_FILE_SUF_ERROR"),
    UPLOAD_OVER_LIMIT(15, "UPLOAD_OVER_LIMIT"),
    UPLOAD_NEED_FILE(16, "UPLOAD_NEED_FILE"),
    FILE_NOT_EXIST(17, "FILE_NOT_EXIST"),

    // 数据相关
    DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE(20, "DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE"),



    //用户相关
    PLA_USER_NOT_EXISTS(30, "PLA_USER_NOT_EXISTS"),
    PLA_USER_CAN_NOT_LOGIN(31, "PLA_USER_CAN_NOT_LOGIN"),
    PLA_USER_PWD_ERROR(32, "PLA_USER_PWD_ERROR"),
    PLA_USER_PWD_NOTSAME_ERROR(33, "PLA_USER_PWD_NOTSAME_ERROR"),
    PLA_USER_EMAIL_NOT_EXISTS(34, "PLA_USER_EMAIL_NOT_EXISTS"),
    PLA_USER_CAPTCHA_SEND_FREQUENT(35, "PLA_USER_CAPTCHA_SEND_FREQUENT"),
    PLA_USER_CAPTCHA_NOT_EXIST(36, "PLA_USER_CAPTCHA_NOT_EXIST"),
    PLA_USER_LOGIN_PASSWORD_ERROR(37, "PLA_USER_LOGIN_PASSWORD_ERROR"),
    PLA_USER_EMAIL_BIND_EXISTS(38, "PLA_USER_EMAIL_BIND_EXISTS"),
    PLA_USER_MOBILE_BIND_EXISTS(39, "PLA_USER_MOBILE_BIND_EXISTS"),
    PLA_USER_NAME_BIND_EXISTS(40, "PLA_USER_NAME_BIND_EXISTS"),
    PLA_USER_EMPLOYEE_ID_EXISTS(41, "PLA_USER_EMPLOYEE_ID_EXISTS"),



    SEND_TEMPLATE_NOT_EXIST(101, "SEND_TEMPLATE_NOT_EXIST"),
    PLA_ROLE_NOT_EXISTS(104, "PLA_ROLE_NOT_EXISTS"),
    PLA_ROLE_NOT_ADMIN(105, "PLA_ROLE_NOT_ADMIN"),
    ROLE_NAME_EXISTS(106, "ROLE_NAME_EXISTS"),


    //业务相关
    BUSINESS_NOT_EXISTS(127, "BUSINESS_NOT_EXISTS"),
    JSON_CONVERT_EXCEPTION(128, "JSON_CONVERT_EXCEPTION"),


    //部门相关
    DEPT_HAS_CHILDREN(141,"DEPT_HAS_CHILDREN" ),
    DEPT_NOT_EXISTS(142,"DEPT_NOT_EXISTS" ),



    // 业务流程类别相关
    PARENT_CATEGORY_NOT_EXISTS(160,"PARENT_CATEGORY_NOT_EXISTS" ),
    CATEGORY_NOT_EXISTS(161,"CATEGORY_NOT_EXISTS" ),
    CATEGORY_HAS_CHILDREN(162,"CATEGORY_HAS_CHILDREN" ),


    // 业务流程类型相关
    PARENT_TYPE_NOT_EXISTS(180,"PARENT_TYPE_NOT_EXISTS" ),
    TYPE_HAS_CHILDREN(181,"TYPE_HAS_CHILDREN" ),

    PASS_TO_PREVIOUS_NODE(182, "PASS_TO_PREVIOUS_NODE"),
    REJECT_TO_SUFFIX_NODE(183, "REJECT_TO_SUFFIX_NODE"),

    FLOW_EXCEPTION(184,"FLOW_EXCEPTION" ),


    // 字典数据相关
    DictDataNotExist(201,"DictDataNotExist" ),


    // 菜单相关
    MENU_HAS_CHILD(221,"MENU_HAS_CHILD" ),
    MENU_NOT_EXIST(222,"MENU_NOT_EXIST" ),


    ENTRUST_ORDER_NOT_EXIST(400, "ENTRUST_ORDER_NOT_EXIST"),
    ENTRUST_ORDER_STATUS_ERROR(401,"ENTRUST_ORDER_STATUS_ERROR" ),
    ENTRUST_ORDER_CUSTOMER_INFO_INCOMPLETE(402,"ENTRUST_ORDER_CUSTOMER_INFO_INCOMPLETE" ),
    ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE(403,"ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE" ),
    ENTRUST_ORDER_ENTRUST_INFO_INCOMPLETE(405,"ENTRUST_ORDER_ENTRUST_INFO_INCOMPLETE" ),
    ENTRUST_ORDER_REPORT_INFO_INCOMPLETE(406,"ENTRUST_ORDER_REPORT_INFO_INCOMPLETE" ),
    ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE(407,"ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE" ),



    // 样本相关
    SAMPLE_PLATE_NUM_NOT_CONSISTENT(500,"SAMPLE_PLATE_NUM_NOT_CONSISTENT" ),





    // 文件相关
    SYSTEM_FILE_TYPE_NOT_EXIST(900,"SYSTEM_FILE_TYPE_NOT_EXIST"),
    SYSTEM_FILE_PARENT_TYPE_NOT_EXIST(900,"SYSTEM_FILE_PARENT_TYPE_NOT_EXIST"),
    SYSTEM_FILE_TYPE_NAME_EXISTS(901,"SYSTEM_FILE_TYPE_NAME_EXISTS" ),
    SYSTEM_FILE_TYPE_HAS_CHILDREN(902,"SYSTEM_FILE_TYPE_HAS_CHILDREN" ),
    SYSTEM_FILE_TYPE_HAS_FILE(903,"SYSTEM_FILE_TYPE_HAS_FILE" ),
    SYSTEM_FILE_NOT_EXIST(904,"SYSTEM_FILE_NOT_EXIST" ),


    // 实验记录相关
    YEAR_CANT_BE_GREATER_THAN_CURRENT_YEAR(1001,"YEAR_CANT_BE_GREATER_THAN_CURRENT_YEAR" ),

    // 存储位置相关
    STORE_POSITION_NOT_EXIST(1100,"STORE_POSITION_NOT_EXIST"),
    STORE_POSITION_PARENT_NOT_EXIST(1101,"STORE_POSITION_PARENT_NOT_EXIST"),
    STORE_POSITION_NAME_EXISTS(1102,"STORE_POSITION_NAME_EXISTS"),
    STORE_POSITION_HAS_CHILDREN(1103,"STORE_POSITION_HAS_CHILDREN"),

    // 字典相关
    DICT_DATA_CATEGORY_NOT_EXIST(1200,"DICT_DATA_CATEGORY_NOT_EXIST" ),
    DICT_DATA_NOT_EXIST(1201,"DICT_DATA_NOT_EXIST" ),
    DICT_DATA_PARENT_NOT_EXIST(1202,"DICT_DATA_PARENT_NOT_EXIST" ),
    DICT_DATA_WAS_EXISTS(1203,"DICT_DATA_WAS_EXISTS" ),
    DICT_DATA_HAS_CHILDREN(1204,"DICT_DATA_HAS_CHILDREN" ),
    DICT_DATA_IS_NOT_VISIBLE(1205,"DICT_DATA_IS_NOT_VISIBLE" ),

    // 文件记录相关
    FILE_RECORD_NOT_EXIST(1300,"FILE_RECORD_NOT_EXIST" ),

    // 试剂相关
    REAGENT_NOT_EXIST(1400,"REAGENT_NOT_EXIST"),
    REAGENT_HAS_SPECIFICATIONS(1401,"REAGENT_HAS_SPECIFICATIONS"),
    REAGENT_SPECIFICATION_NOT_EXIST(1402,"REAGENT_SPECIFICATION_NOT_EXIST"),
    INVALID_REAGENT_BRAND_DICT(1403,"INVALID_REAGENT_BRAND_DICT"),
    INVALID_REAGENT_UNIT_DICT(1404,"INVALID_REAGENT_UNIT_DICT"),
    INVALID_REAGENT_STORAGE_CONDITION_DICT(1405,"INVALID_REAGENT_STORAGE_CONDITION_DICT"),

    // 检测方法相关
    DETECTION_METHOD_NOT_EXIST(1400,"DETECTION_METHOD_NOT_EXIST" ),
    DETECTION_METHOD_BIND_PROJECT(1401,"DETECTION_METHOD_BIND_PROJECT" ),

    // 检测项目相关
    DETECTION_PROJECT_NOT_EXIST(1500,"DETECTION_PROJECT_NOT_EXIST" ),
    DETECTION_PROJECT_BIND_ENTRUST_ORDER(1501,"DETECTION_PROJECT_BIND_ENTRUST_ORDER" ),

    // 执行标准相关
    EXECUTION_STANDARD_NOT_EXIST(1600,"EXECUTION_STANDARD_NOT_EXIST" ),

    // 模板文件相关
    TEMPLATE_FILE_NOT_EXIST(1700,"TEMPLATE_FILE_NOT_EXIST" ),

    // 设备相关
    EQUIPMENT_NOT_EXIST(1800,"EQUIPMENT_NOT_EXIST" ),
    ;

    private final Integer code;

    private final String desc;

    PcsResultCode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 查询异常类型
     *
     * @param code 异常code
     * @return enum pcs
     */
    public static PcsResultCode convert(Integer code) {
        for (PcsResultCode returnCode : values()) {
            if (code.equals(returnCode.code)) {
                return returnCode;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
