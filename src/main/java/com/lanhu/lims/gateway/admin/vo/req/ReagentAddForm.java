package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/********************************
 * @title ReagentAddForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 新增试剂入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "新增试剂入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReagentAddForm {
    /**
     * 试剂名称
     */
    @ApiModelProperty(value = "试剂名称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String name;

    /**
     * 试剂简称
     */
    @ApiModelProperty(value = "试剂简称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String shortName;
}
