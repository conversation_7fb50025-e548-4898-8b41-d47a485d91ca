package com.lanhu.lims.gateway.admin.utils.sequence;

import com.lanhu.lims.gateway.admin.utils.sequence.formatter.SequenceFormatter;
import com.lanhu.lims.gateway.admin.utils.sequence.formatter.SequenceFormatterFactory;
import com.lanhu.lims.gateway.admin.utils.sequence.generator.DateBasedSequenceNameGenerator;
import com.lanhu.lims.gateway.admin.utils.sequence.generator.DynamicSequenceNameGenerator;
import lombok.AllArgsConstructor;
import lombok.Getter;

/********************************
 * @title SequenceNameEnum
 * @package com.lanhu.lims.gateway.admin.utils.sequence
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/9 21:48
 * @version 0.0.1
 *********************************/
@Getter
@AllArgsConstructor
public enum SequenceNameEnum {
    /**
     * 文件编号 - 使用5位补零格式 (00001, 00002...)
     */
    SYSTEM_FILE_CODE("SYSTEM_FILE_CODE", 1, 1, SequenceFormatterFactory.zeroPadding(5)),

    /**
     * 委托单号 - 使用年月+5位补零格式 (WT20210100001, WT20210100002...)
     */
    ENTRUST_ORDER_NO("WT", 1, 1, SequenceFormatterFactory.customPattern("WT{yyyy}{MM}{seq:5}")),

    /**
     * 样本编号 - 使用年月+6位补零格式 (ZKW202506000001, ZKW202506000002, ...)
     */
    SAMPLE_CODE("ZKW", 1, 1, SequenceFormatterFactory.customPattern("ZKW{yyyy}{MM}{seq:6}")),

    /**
     * 检测项目编号 - 使用XM+4位补零格式 (XM0001, XM0002, ...)
     */
    DETECTION_PROJECT_CODE("XM", 1, 1, SequenceFormatterFactory.prefixSequence("XM", 4)),

    /**
     * 模板文件编号 - 使用MB+4位补零格式 (MB0001, MB0002, ...)
     */
    TEMPLATE_FILE_CODE("MB", 1, 1, SequenceFormatterFactory.prefixSequence("MB", 4)),

    /**
     * 仪器设备编号 - 使用YQ+4位补零格式 (YQ0001, YQ0002, ...)
     */
    EQUIPMENT_CODE("YQ", 1, 1, SequenceFormatterFactory.prefixSequence("YQ", 4)),

    /**
     * 试剂编号 - 使用SJ+4位补零格式 (SJ0001, SJ0002, ...)
     */
    REAGENT_CODE("SJ", 1, 1, SequenceFormatterFactory.prefixSequence("SJ", 4))
    ;



    private final String name;

    private final int currentValue;

    private final int increment;

    private final SequenceFormatter formatter;

    /**
     * 获取动态序列名称生成器（按月）
     *
     * @return 动态序列名称生成器
     */
    public DynamicSequenceNameGenerator getMonthlyNameGenerator() {
        return DateBasedSequenceNameGenerator.yearMonth(this.name);
    }

    /**
     * 获取动态序列名称生成器（按日）
     *
     * @return 动态序列名称生成器
     */
    public DynamicSequenceNameGenerator getDailyNameGenerator() {
        return DateBasedSequenceNameGenerator.yearMonthDay(this.name);
    }

    /**
     * 获取动态序列名称生成器（按年）
     *
     * @return 动态序列名称生成器
     */
    public DynamicSequenceNameGenerator getYearlyNameGenerator() {
        return DateBasedSequenceNameGenerator.year(this.name);
    }
}
