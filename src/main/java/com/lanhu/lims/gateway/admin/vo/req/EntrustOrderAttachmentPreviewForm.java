package com.lanhu.lims.gateway.admin.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 委托单附件预览入参
 * @author: huangzheng
 * @date: 2025/6/17 10:59
 */

@Data
@ApiModel(value = "委托单附件预览入参",description = "委托单附件预览入参")
public class EntrustOrderAttachmentPreviewForm {




    // 附件的fileId
    @ApiModelProperty(value = "附件的fileId",required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long fileId;



}
