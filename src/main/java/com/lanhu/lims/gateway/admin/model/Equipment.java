package com.lanhu.lims.gateway.admin.model;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/********************************
 * @title Equipment
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/14 22:23
 * @version 0.0.1
 *********************************/

@ApiModel(description = "仪器设备表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_equipment")
public class Equipment {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 仪器设备编号（格式示例：YQ0001）
     */
    @TableField(value = "equipment_code")
    @ApiModelProperty(value = "仪器设备编号（格式示例：YQ0001）")
    private String equipmentCode;

    /**
     * 仪器设备名称
     */
    @TableField(value = "equipment_name")
    @ApiModelProperty(value = "仪器设备名称")
    private String equipmentName;

    /**
     * 仪器用途（0:检测，1:分析，2:其他，多个用逗号隔开）
     */
    @TableField(value = "`usage`")
    @ApiModelProperty(value = "仪器用途（0:检测，1:分析，2:其他，多个用逗号隔开）")
    private String usage;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 型号
     */
    @TableField(value = "model")
    @ApiModelProperty(value = "型号")
    private String model;

    /**
     * 仪器所属（0：自有，1：租赁）
     */
    @TableField(value = "ownership_status")
    @ApiModelProperty(value = "仪器所属（0：自有，1：租赁）")
    private Integer ownershipStatus;

    /**
     * 检测能力 用/分隔
     */
    @TableField(value = "inspect_item")
    @ApiModelProperty(value = "检测能力 用/分隔")
    private String inspectItem;

    /**
     * 收货人
     */
    @TableField(value = "consignee")
    @ApiModelProperty(value = "收货人")
    private String consignee;

    /**
     * 收货日期 yyyy-MM-dd
     */
    @TableField(value = "receipt_date")
    @ApiModelProperty(value = "收货日期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String receiptDate;

    /**
     * 维护人
     */
    @TableField(value = "maintenance_user")
    @ApiModelProperty(value = "维护人")
    private String maintenanceUser;

    /**
     * 最近维护日期
     */
    @TableField(value = "last_maintenance_date")
    @ApiModelProperty(value = "最近维护日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastMaintenanceDate;

    /**
     * 下次维修时间
     */
    @TableField(value = "next_maintenance_date")
    @ApiModelProperty(value = "下次维修时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date nextMaintenanceDate;

    /**
     * 状态（0：正常，1:维修中，2:已废弃）
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态（0：正常，1:维修中，2:已废弃）")
    private Integer status;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    @JsonIgnore
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    @JsonIgnore
    private Integer auditStatus;

    /**
     * 最近一次审核人ID
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "最近一次审核人ID")
    @JsonIgnore
    private Long auditBy;

    /**
     * 最近一次审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "最近一次审核时间")
    @JsonIgnore
    private Date auditTime;

    /**
     * 最近一次审核人名称
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "最近一次审核人名称")
    @JsonIgnore
    private String auditName;

    /**
     * 审核备注/审核不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "审核备注/审核不通过原因")
    @JsonIgnore
    private String auditRemark;

    /**
     * 仪器用途名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "仪器用途名称")
    private List<String> usageNameList = new ArrayList<>();

    /**
     * 品牌名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "品牌名称")
    private String brandName = StrUtil.EMPTY;

    /**
     * 仪器所属名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "仪器所属名称")
    private String ownershipStatusName = StrUtil.EMPTY;

    /**
     * 检测能力列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "检测能力列表")
    private List<DetectionMethod> inspectItemList = new ArrayList<>();

    /**
     * 状态名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "状态名称")
    private String statusName = StrUtil.EMPTY;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "附件列表")
    private List<MaterialAttachment> attachments = new ArrayList<>();
}