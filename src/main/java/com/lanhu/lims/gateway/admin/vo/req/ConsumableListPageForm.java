package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lanhu.lims.gateway.admin.core.PageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/********************************
 * @title ConsumableListPageForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 查询耗材分页列表入参
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@ApiModel(value = "查询耗材分页列表入参")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumableListPageForm extends PageForm {
    /**
     * 耗材编号
     */
    @ApiModelProperty(value = "耗材编号")
    private String consumableCode;

    /**
     * 耗材名称
     */
    @ApiModelProperty(value = "耗材名称")
    private String consumableName;

    /**
     * 耗材简称
     */
    @ApiModelProperty(value = "耗材简称")
    private String shortName;

    /**
     * 状态：0: 禁用 1: 启用
     */
    @ApiModelProperty(value = "状态：0: 禁用 1: 启用")
    private Integer status;
}
