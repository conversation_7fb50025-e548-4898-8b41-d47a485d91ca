package com.lanhu.lims.gateway.admin.service;

import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.mapper.ReagentMapper;
import com.lanhu.lims.gateway.admin.mapper.ReagentSpecificationMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/********************************
 * @title ReagentService
 * @package com.lanhu.lims.gateway.admin.service
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 12:54
 * @version 0.0.1
 *********************************/
@Service
public class ReagentService {
    @Resource
    private ReagentMapper reagentMapper;

    @Resource
    private ReagentSpecificationMapper reagentSpecificationMapper;

    @Resource
    private DictDataMapper dictDataMapper;
}
