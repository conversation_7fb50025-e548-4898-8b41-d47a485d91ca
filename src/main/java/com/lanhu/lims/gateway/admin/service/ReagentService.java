package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanhu.lims.gateway.admin.core.BusinessException;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.AuditStatusEnum;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.mapper.ReagentMapper;
import com.lanhu.lims.gateway.admin.mapper.ReagentSpecificationMapper;
import com.lanhu.lims.gateway.admin.model.AdminUser;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.model.Reagent;
import com.lanhu.lims.gateway.admin.model.ReagentSpecification;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ReagentService
 * @package com.lanhu.lims.gateway.admin.service
 * @description 试剂管理服务
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@Service
public class ReagentService {
    @Resource
    private ReagentMapper reagentMapper;

    @Resource
    private ReagentSpecificationMapper reagentSpecificationMapper;

    @Resource
    private DictDataMapper dictDataMapper;

    @Resource
    private DictDataService dictDataService;

    /**
     * 查询试剂列表
     *
     * @param form 查询入参
     * @return 列表
     */
    @DS("slave_1")
    public PcsResult<List<Reagent>> list(ReagentListForm form) {
        LambdaQueryWrapper<Reagent> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Reagent::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(Reagent::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getCode()), Reagent::getCode, form.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getName()), Reagent::getName, form.getName());
        wrapper.like(StrUtil.isNotBlank(form.getShortName()), Reagent::getShortName, form.getShortName());
        wrapper.orderByDesc(Reagent::getCreateTime);
        return Result.ok(reagentMapper.selectList(wrapper));
    }

    /**
     * 分页查询试剂
     *
     * @param form 查询入参
     * @return 分页列表
     */
    @DS("slave_1")
    public PcsResult<IPage<Reagent>> listPage(ReagentListPageForm form) {
        LambdaQueryWrapper<Reagent> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Reagent::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(form.getStatus() != null, Reagent::getStatus, form.getStatus());
        wrapper.like(StrUtil.isNotBlank(form.getCode()), Reagent::getCode, form.getCode());
        wrapper.like(StrUtil.isNotBlank(form.getName()), Reagent::getName, form.getName());
        wrapper.like(StrUtil.isNotBlank(form.getShortName()), Reagent::getShortName, form.getShortName());
        wrapper.orderByDesc(Reagent::getCreateTime);
        Page<Reagent> page = new Page<>(form.getPageIndex(), form.getPageSize());
        return Result.ok(reagentMapper.selectPage(page, wrapper));
    }

    /**
     * 查询试剂详情
     *
     * @param form 查询入参
     * @return 详情
     */
    @DS("slave_1")
    public PcsResult<Reagent> detail(ReagentSingleForm form) {
        Reagent reagent = reagentMapper.selectById(form.getId());
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        // 查询关联的规格信息
        LambdaQueryWrapper<ReagentSpecification> specWrapper = Wrappers.lambdaQuery();
        specWrapper.eq(ReagentSpecification::getReagentId, reagent.getId());
        specWrapper.eq(ReagentSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        specWrapper.orderByDesc(ReagentSpecification::getCreateTime);
        List<ReagentSpecification> specifications = reagentSpecificationMapper.selectList(specWrapper);
        return Result.ok(reagent);
    }

    /**
     * 新增试剂
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> add(ReagentAddForm form, AdminUser adminUser) {
        // 生成试剂编号
        String reagentCode = SequenceUtil.getNextFormattedValue(SequenceNameEnum.REAGENT_CODE);
        // 新增试剂
        Reagent reagent = new Reagent();
        reagent.setCode(reagentCode);
        reagent.setName(form.getName());
        reagent.setShortName(form.getShortName());
        reagent.setStatus(EnableEnum.ENABLE.getCode());
        reagent.setIsEffect(IsEffectEnum.NORMAL.getCode());
        reagent.setCreateBy(adminUser.getId());
        reagent.setCreateName(adminUser.getRealName());
        reagent.setCreateTime(DateUtil.date());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagent.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        reagentMapper.insert(reagent);
        // todo 发起流程
        return Result.ok();
    }

    /**
     * 修改试剂
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> edit(ReagentEditForm form, AdminUser adminUser) {
        // 校验数据
        Reagent reagent = checkCanEditOrDelete(form.getId());
        // 修改试剂
        reagent.setName(form.getName());
        reagent.setShortName(form.getShortName());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagent.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        reagentMapper.updateById(reagent);
        // todo 发起流程
        return Result.ok();
    }

    /**
     * 删除试剂
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> del(ReagentSingleForm form, AdminUser adminUser) {
        // 校验数据
        Reagent reagent = checkCanEditOrDelete(form.getId());
        // 校验是否有规格数据
        LambdaQueryWrapper<ReagentSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReagentSpecification::getReagentId, form.getId());
        wrapper.eq(ReagentSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        Long count = reagentSpecificationMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessException(PcsResultCode.REAGENT_HAS_SPECIFICATIONS);
        }
        // 删除试剂
        reagent.setIsEffect(IsEffectEnum.DELETE.getCode());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagent.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        reagentMapper.updateById(reagent);
        // todo 发起流程
        return Result.ok();
    }

    /**
     * 启用试剂
     *
     * @param form      启用入参
     * @param adminUser 当前用户
     * @return 结果
     */
    @DS("master_1")
    public PcsResult<Void> enable(ReagentSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.ENABLE, adminUser);
        return Result.ok();
    }

    /**
     * 禁用试剂
     *
     * @param form      禁用入参
     * @param adminUser 当前用户
     * @return 结果
     */
    @DS("master_1")
    public PcsResult<Void> disable(ReagentSingleForm form, AdminUser adminUser) {
        // 更新状态
        updateStatus(form.getId(), EnableEnum.DISABLE, adminUser);
        return Result.ok();
    }

    /**
     * 新增试剂规格
     *
     * @param form      新增入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> addSpecification(ReagentSpecificationAddForm form, AdminUser adminUser) {
        // 校验试剂是否存在
        Reagent reagent = reagentMapper.selectById(form.getReagentId());
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        // 校验字典数据
        validateDictData(form.getBrand(), DictDataTypeEnum.REAGENT_BRAND, PcsResultCode.INVALID_REAGENT_BRAND_DICT);
        validateDictData(form.getUnit(), DictDataTypeEnum.REAGENT_UNIT, PcsResultCode.INVALID_REAGENT_UNIT_DICT);
        validateDictData(form.getStorageCondition(), DictDataTypeEnum.REAGENT_STORAGE_CONDITION, PcsResultCode.INVALID_REAGENT_STORAGE_CONDITION_DICT);
        // 新增规格
        ReagentSpecification specification = new ReagentSpecification();
        specification.setReagentId(form.getReagentId());
        specification.setBrand(form.getBrand());
        specification.setCatalogNumber(StrUtil.blankToDefault(form.getCatalogNumber(), StrUtil.EMPTY));
        specification.setSpecification(StrUtil.blankToDefault(form.getSpecification(), StrUtil.EMPTY));
        specification.setUnit(form.getUnit());
        specification.setStorageCondition(form.getStorageCondition());
        specification.setIsEffect(IsEffectEnum.NORMAL.getCode());
        specification.setCreateBy(adminUser.getId());
        specification.setCreateName(adminUser.getRealName());
        specification.setCreateTime(DateUtil.date());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        reagentSpecificationMapper.insert(specification);
        return Result.ok();
    }

    /**
     * 修改试剂规格
     *
     * @param form      修改入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> editSpecification(ReagentSpecificationEditForm form, AdminUser adminUser) {
        // 校验规格是否存在
        ReagentSpecification specification = reagentSpecificationMapper.selectById(form.getId());
        if (specification == null || specification.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_SPECIFICATION_NOT_EXIST);
        }
        // 校验字典数据
        validateDictData(form.getBrand(), DictDataTypeEnum.REAGENT_BRAND, PcsResultCode.INVALID_REAGENT_BRAND_DICT);
        validateDictData(form.getUnit(), DictDataTypeEnum.REAGENT_UNIT, PcsResultCode.INVALID_REAGENT_UNIT_DICT);
        validateDictData(form.getStorageCondition(), DictDataTypeEnum.REAGENT_STORAGE_CONDITION, PcsResultCode.INVALID_REAGENT_STORAGE_CONDITION_DICT);
        // 修改规格
        specification.setBrand(form.getBrand());
        specification.setCatalogNumber(StrUtil.blankToDefault(form.getCatalogNumber(), StrUtil.EMPTY));
        specification.setSpecification(StrUtil.blankToDefault(form.getSpecification(), StrUtil.EMPTY));
        specification.setUnit(form.getUnit());
        specification.setStorageCondition(form.getStorageCondition());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        reagentSpecificationMapper.updateById(specification);
        return Result.ok();
    }

    /**
     * 删除试剂规格
     *
     * @param form      删除入参
     * @param adminUser 当前用户
     */
    @DS("master_1")
    public PcsResult<Void> delSpecification(ReagentSpecificationSingleForm form, AdminUser adminUser) {
        // 校验规格是否存在
        ReagentSpecification specification = reagentSpecificationMapper.selectById(form.getId());
        if (specification == null || specification.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_SPECIFICATION_NOT_EXIST);
        }
        // 删除规格
        specification.setIsEffect(IsEffectEnum.DELETE.getCode());
        specification.setUpdateBy(adminUser.getId());
        specification.setUpdateName(adminUser.getRealName());
        specification.setUpdateTime(DateUtil.date());
        reagentSpecificationMapper.updateById(specification);
        return Result.ok();
    }

    /**
     * 获取试剂品牌字典选项
     *
     * @return 品牌选项列表
     */
    @DS("slave_1")
    public PcsResult<List<DictData>> getBrandOptions() {
        DictDataListByTypeForm form = new DictDataListByTypeForm();
        form.setDictType(DictDataTypeEnum.REAGENT_BRAND.getCode());
        return dictDataService.listByType(form);
    }

    /**
     * 获取试剂计量单位字典选项
     *
     * @return 计量单位选项列表
     */
    @DS("slave_1")
    public PcsResult<List<DictData>> getUnitOptions() {
        DictDataListByTypeForm form = new DictDataListByTypeForm();
        form.setDictType(DictDataTypeEnum.REAGENT_UNIT.getCode());
        return dictDataService.listByType(form);
    }

    /**
     * 获取试剂存储条件字典选项
     *
     * @return 存储条件选项列表
     */
    @DS("slave_1")
    public PcsResult<List<DictData>> getStorageConditionOptions() {
        DictDataListByTypeForm form = new DictDataListByTypeForm();
        form.setDictType(DictDataTypeEnum.REAGENT_STORAGE_CONDITION.getCode());
        return dictDataService.listByType(form);
    }

    /**
     * 查询试剂的规格列表
     *
     * @param reagentId 试剂ID
     * @return 规格列表
     */
    @DS("slave_1")
    public PcsResult<List<ReagentSpecification>> listSpecifications(Long reagentId) {
        LambdaQueryWrapper<ReagentSpecification> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReagentSpecification::getReagentId, reagentId);
        wrapper.eq(ReagentSpecification::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.orderByDesc(ReagentSpecification::getCreateTime);
        return Result.ok(reagentSpecificationMapper.selectList(wrapper));
    }

    // ================================================================== 私有方法 ================================================================== //

    /**
     * 校验试剂是否可以编辑或删除
     *
     * @param id 试剂ID
     * @return 试剂对象
     */
    private Reagent checkCanEditOrDelete(Long id) {
        Reagent reagent = reagentMapper.selectById(id);
        if (reagent == null || reagent.getIsEffect() != IsEffectEnum.NORMAL.getCode()) {
            throw new BusinessException(PcsResultCode.REAGENT_NOT_EXIST);
        }
        return reagent;
    }

    /**
     * 更新试剂状态
     *
     * @param id         试剂ID
     * @param status     状态
     * @param adminUser  当前用户
     */
    private void updateStatus(Long id, EnableEnum status, AdminUser adminUser) {
        Reagent reagent = checkCanEditOrDelete(id);
        reagent.setStatus(status.getCode());
        reagent.setUpdateBy(adminUser.getId());
        reagent.setUpdateName(adminUser.getRealName());
        reagent.setUpdateTime(DateUtil.date());
        reagentMapper.updateById(reagent);
    }

    /**
     * 校验字典数据是否有效
     *
     * @param dictValue 字典值
     * @param dictType  字典类型
     * @param errorCode 错误码
     */
    private void validateDictData(String dictValue, DictDataTypeEnum dictType, PcsResultCode errorCode) {
        if (StrUtil.isBlank(dictValue)) {
            return;
        }
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode());
        wrapper.eq(DictData::getStatus, EnableEnum.ENABLE.getCode());
        wrapper.eq(DictData::getDictValue, dictValue);
        wrapper.apply("parent_id IN (SELECT id FROM t_dict_data WHERE dict_value = '" + dictType.getCode() + "' AND is_effect = 0)");
        Long count = dictDataMapper.selectCount(wrapper);
        if (count == 0) {
            throw new BusinessException(errorCode);
        }
    }
}
