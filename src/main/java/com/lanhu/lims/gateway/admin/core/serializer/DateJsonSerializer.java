package com.lanhu.lims.gateway.admin.core.serializer;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;

import java.io.IOException;

/**
 * create by yiyongyao Date:2018/3/9 下午3:50 Description: 将雪花生成器的id转为字符id ,
 * 传递到页面不会丢失精度
 */
public class DateJsonSerializer extends JsonSerializer<Long> {

    @Override
    public void serialize(Long aLong, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (aLong != null) {
            //如果单位是秒
            if(String.valueOf(aLong).length() == 10){
                aLong = aLong*1000L;
            }
            jsonGenerator.writeString(DateUtil.format(DateUtil.date(aLong), ProjectConstant.DATE_TIME_FORMAT));
        }
    }


}
