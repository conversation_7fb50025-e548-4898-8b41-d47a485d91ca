package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Reagent;
import com.lanhu.lims.gateway.admin.model.ReagentSpecification;
import com.lanhu.lims.gateway.admin.service.ReagentService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ReagentController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "试剂相关接口", value = "试剂相关接口")
public class ReagentController extends BaseController {

    @Resource
    private ReagentService reagentService;

    /**
     * 查询试剂列表
     */
    @ApiOperation(value = "查询试剂列表")
    @PostMapping("/reagent/v1/list")
    public PcsResult<List<Reagent>> list(@Validated @RequestBody ReagentListForm form) {
        return reagentService.list(form);
    }

    /**
     * 查询试剂分页列表
     */
    @ApiOperation(value = "查询试剂分页列表")
    @PostMapping("/reagent/v1/listPage")
    public PcsResult<IPage<Reagent>> listPage(@Validated @RequestBody ReagentListPageForm form) {
        return reagentService.listPage(form);
    }

    /**
     * 查询试剂详情
     */
    @ApiOperation(value = "查询试剂详情")
    @PostMapping("/reagent/v1/detail")
    public PcsResult<Reagent> detail(@Validated @RequestBody ReagentSingleForm form) {
        return reagentService.detail(form);
    }

    /**
     * 新增试剂
     */
    @ApiOperation(value = "新增试剂")
    @PostMapping("/reagent/v1/add")
    @Log(title = "新增试剂", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody ReagentAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.add(form, loginUser.getSysUser());
    }

    /**
     * 修改试剂
     */
    @ApiOperation(value = "修改试剂")
    @PostMapping("/reagent/v1/edit")
    @Log(title = "修改试剂", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody ReagentEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.edit(form, loginUser.getSysUser());
    }

    /**
     * 删除试剂
     */
    @ApiOperation(value = "删除试剂")
    @PostMapping("/reagent/v1/del")
    @Log(title = "删除试剂", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody ReagentSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.del(form, loginUser.getSysUser());
    }

    /**
     * 启用试剂
     */
    @ApiOperation(value = "启用试剂")
    @PostMapping("/reagent/v1/enable")
    @Log(title = "启用试剂", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody ReagentSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.enable(form, loginUser.getSysUser());
    }

    /**
     * 禁用试剂
     */
    @ApiOperation(value = "禁用试剂")
    @PostMapping("/reagent/v1/disable")
    @Log(title = "禁用试剂", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody ReagentSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.disable(form, loginUser.getSysUser());
    }

    /**
     * 查询试剂规格分页列表
     */
    @ApiOperation(value = "查询试剂规格分页列表")
    @PostMapping("/reagent/specification/v1/listPage")
    public PcsResult<IPage<ReagentSpecification>> listPageSpecifications(@Validated @RequestBody ReagentSpecificationListPageForm form) {
        return reagentService.listPageSpecifications(form);
    }

    /**
     * 新增试剂规格
     */
    @ApiOperation(value = "新增试剂规格")
    @PostMapping("/reagent/specification/v1/add")
    @Log(title = "新增试剂规格", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> addSpecification(@Validated @RequestBody ReagentSpecificationAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.addSpecification(form, loginUser.getSysUser());
    }

    /**
     * 修改试剂规格
     */
    @ApiOperation(value = "修改试剂规格")
    @PostMapping("/reagent/specification/v1/edit")
    @Log(title = "修改试剂规格", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> editSpecification(@Validated @RequestBody ReagentSpecificationEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.editSpecification(form, loginUser.getSysUser());
    }

    /**
     * 删除试剂规格
     */
    @ApiOperation(value = "删除试剂规格")
    @PostMapping("/reagent/specification/v1/del")
    @Log(title = "删除试剂规格", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> delSpecification(@Validated @RequestBody ReagentSpecificationSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.delSpecification(form, loginUser.getSysUser());
    }

    /**
     * 查询试剂规格分页列表
     */
    @ApiOperation(value = "查询试剂规格分页列表")
    @PostMapping("/reagent/specification/v1/listPage")
    public PcsResult<IPage<ReagentSpecification>> listSpecificationPage(@Validated @RequestBody ReagentSpecificationListPageForm form) {
        return reagentService.listPageSpecifications(form);
    }

    /**
     * 查询试剂规格列表
     */
    @ApiOperation(value = "查询试剂规格列表")
    @PostMapping("/reagent/specification/v1/list")
    public PcsResult<List<ReagentSpecification>> listSpecifications(@Validated @RequestBody ReagentSpecificationListForm form) {
        return reagentService.listSpecifications(form);
    }
}
