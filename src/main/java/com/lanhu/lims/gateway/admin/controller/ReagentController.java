package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Reagent;
import com.lanhu.lims.gateway.admin.model.ReagentSpecification;
import com.lanhu.lims.gateway.admin.service.ReagentService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ReagentController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description 试剂管理控制器
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@Api(tags = "试剂管理")
@RestController
@RequestMapping("/api")
public class ReagentController extends BaseController {

    @Resource
    private ReagentService reagentService;

    /**
     * 查询试剂列表
     */
    @ApiOperation(value = "查询试剂列表")
    @PostMapping("/reagent/v1/list")
    public PcsResult<List<Reagent>> list(@RequestBody ReagentListForm form) {
        return reagentService.list(form);
    }

    /**
     * 分页查询试剂
     */
    @ApiOperation(value = "分页查询试剂")
    @PostMapping("/reagent/v1/listPage")
    public PcsResult<IPage<Reagent>> listPage(@RequestBody ReagentListPageForm form) {
        return reagentService.listPage(form);
    }

    /**
     * 查询试剂详情
     */
    @ApiOperation(value = "查询试剂详情")
    @PostMapping("/reagent/v1/detail")
    public PcsResult<Reagent> detail(@RequestBody ReagentSingleForm form) {
        return reagentService.detail(form);
    }

    /**
     * 新增试剂
     */
    @ApiOperation(value = "新增试剂")
    @PostMapping("/reagent/v1/add")
    public PcsResult<Void> add(@Validated @RequestBody ReagentAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.add(form, loginUser.getAdminUser());
    }

    /**
     * 修改试剂
     */
    @ApiOperation(value = "修改试剂")
    @PostMapping("/reagent/v1/edit")
    public PcsResult<Void> edit(@Validated @RequestBody ReagentEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.edit(form, loginUser.getAdminUser());
    }

    /**
     * 删除试剂
     */
    @ApiOperation(value = "删除试剂")
    @PostMapping("/reagent/v1/del")
    public PcsResult<Void> del(@Validated @RequestBody ReagentSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.del(form, loginUser.getAdminUser());
    }

    /**
     * 启用试剂
     */
    @ApiOperation(value = "启用试剂")
    @PostMapping("/reagent/v1/enable")
    public PcsResult<Void> enable(@Validated @RequestBody ReagentSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.enable(form, loginUser.getAdminUser());
    }

    /**
     * 禁用试剂
     */
    @ApiOperation(value = "禁用试剂")
    @PostMapping("/reagent/v1/disable")
    public PcsResult<Void> disable(@Validated @RequestBody ReagentSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.disable(form, loginUser.getAdminUser());
    }

    /**
     * 新增试剂规格
     */
    @ApiOperation(value = "新增试剂规格")
    @PostMapping("/reagent/v1/addSpecification")
    public PcsResult<Void> addSpecification(@Validated @RequestBody ReagentSpecificationAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.addSpecification(form, loginUser.getAdminUser());
    }

    /**
     * 修改试剂规格
     */
    @ApiOperation(value = "修改试剂规格")
    @PostMapping("/reagent/v1/editSpecification")
    public PcsResult<Void> editSpecification(@Validated @RequestBody ReagentSpecificationEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.editSpecification(form, loginUser.getAdminUser());
    }

    /**
     * 删除试剂规格
     */
    @ApiOperation(value = "删除试剂规格")
    @PostMapping("/reagent/v1/delSpecification")
    public PcsResult<Void> delSpecification(@Validated @RequestBody ReagentSpecificationSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return reagentService.delSpecification(form, loginUser.getAdminUser());
    }

    /**
     * 查询试剂规格列表
     */
    @ApiOperation(value = "查询试剂规格列表")
    @GetMapping("/reagent/v1/listSpecifications")
    public PcsResult<List<ReagentSpecification>> listSpecifications(@RequestParam Long reagentId) {
        return reagentService.listSpecifications(reagentId);
    }

    /**
     * 获取试剂品牌字典选项
     */
    @ApiOperation(value = "获取试剂品牌字典选项")
    @GetMapping("/reagent/v1/getBrandOptions")
    public PcsResult<List<DictData>> getBrandOptions() {
        return reagentService.getBrandOptions();
    }

    /**
     * 获取试剂计量单位字典选项
     */
    @ApiOperation(value = "获取试剂计量单位字典选项")
    @GetMapping("/reagent/v1/getUnitOptions")
    public PcsResult<List<DictData>> getUnitOptions() {
        return reagentService.getUnitOptions();
    }

    /**
     * 获取试剂存储条件字典选项
     */
    @ApiOperation(value = "获取试剂存储条件字典选项")
    @GetMapping("/reagent/v1/getStorageConditionOptions")
    public PcsResult<List<DictData>> getStorageConditionOptions() {
        return reagentService.getStorageConditionOptions();
    }
}
