package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/********************************
 * @title MaterialInventoryBatch
 * @package com.lanhu.lims.gateway.admin.model
 * @description description
 *
 * <AUTHOR>
 * @date 2025/6/17 12:20
 * @version 0.0.1
 *********************************/

@ApiModel(description = "物料出入库批次表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_material_inventory_batch")
public class MaterialInventoryBatch {
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "批次ID")
    private Long id;

    /**
     * 批次编码
     */
    @TableField(value = "batch_code")
    @ApiModelProperty(value = "批次编码")
    private String batchCode;

    /**
     * 试剂/耗材名称多个以逗号隔开
     */
    @TableField(value = "material_name")
    @ApiModelProperty(value = "试剂/耗材名称多个以逗号隔开")
    private String materialName;

    /**
     * 操作类型(0：入库，1：出库)
     */
    @TableField(value = "operation_type")
    @ApiModelProperty(value = "操作类型(0：入库，1：出库)")
    private Integer operationType;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否有效，0：正常，1：删除
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：正常，1：删除")
    private Integer isEffect;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private Long updateBy;

    /**
     * 修改人姓名
     */
    @TableField(value = "update_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 审核人ID
     */
    @TableField(value = "audit_by")
    @ApiModelProperty(value = "审核人ID")
    private Long auditBy;

    /**
     * 审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 审核人名称
     */
    @TableField(value = "audit_name")
    @ApiModelProperty(value = "审核人名称")
    private String auditName;

    /**
     * 状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "状态(-2: 审核不通过 0:待审核 1: 审核中 2:审核通过/正常)")
    private Integer auditStatus;

    /**
     * 物料类型（ 1：试剂, 2：耗材）
     */
    @TableField(value = "material_type")
    @ApiModelProperty(value = "物料类型（ 1：试剂, 2：耗材）")
    private Integer materialType;

    /**
     * 最近一次审批备注或者审批不通过原因
     */
    @TableField(value = "audit_remark")
    @ApiModelProperty(value = "最近一次审批备注或者审批不通过原因")
    private String auditRemark;
}