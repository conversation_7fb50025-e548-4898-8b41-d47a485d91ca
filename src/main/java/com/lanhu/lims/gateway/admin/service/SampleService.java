package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.EntrustOrderStatusEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.enums.SampleFormEnum;
import com.lanhu.lims.gateway.admin.enums.SampleStatusEnum;
import com.lanhu.lims.gateway.admin.mapper.EntrustOrderMapper;
import com.lanhu.lims.gateway.admin.mapper.FileRecordMapper;
import com.lanhu.lims.gateway.admin.mapper.SampleMapper;
import com.lanhu.lims.gateway.admin.model.EntrustOrder;
import com.lanhu.lims.gateway.admin.model.FileRecord;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.model.Sample;
import com.lanhu.lims.gateway.admin.utils.excel.ExcelUtil;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceNameEnum;
import com.lanhu.lims.gateway.admin.utils.sequence.SequenceUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.SampleFormAndCountVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 样本相关服务
 * @author: huangzheng
 * @date: 2025/6/10 9:46
 */

@Service
public class SampleService {


    @Resource
    private SampleMapper sampleMapper;


    @Resource
    private EntrustOrderMapper entrustOrderMapper;


    @Resource
    private FileRecordMapper fileRecordMapper;

    /**
     * 终止指定委托单关联的样本
     */
    @LhTransaction
    @DS("master_1")
    public void terminationByEntrustOrderId(Long entrustOrderId, LoginUser loginUser) {

        LambdaUpdateWrapper<Sample> sampleLambdaUpdateWrapper = Wrappers.lambdaUpdate();

        sampleLambdaUpdateWrapper.eq(Sample::getEntrustOrderId, entrustOrderId);
        sampleLambdaUpdateWrapper.set(Sample::getStatus, SampleStatusEnum.TERMINATED.getCode());
        sampleLambdaUpdateWrapper.set(Sample::getUpdateBy, loginUser.getUserid());
        sampleLambdaUpdateWrapper.set(Sample::getUpdateTime, new Date());
        sampleLambdaUpdateWrapper.set(Sample::getUpdateName, loginUser.getRealName());

        sampleMapper.update(null,sampleLambdaUpdateWrapper);
    }



    /**
     * 删除指定委托单关联的样本
     */
    @LhTransaction
    @DS("master_1")
    public void delByEntrustOrderId(Long entrustOrderId, LoginUser loginUser) {

        LambdaUpdateWrapper<Sample> sampleLambdaUpdateWrapper = Wrappers.lambdaUpdate();


        sampleLambdaUpdateWrapper.eq(Sample::getEntrustOrderId, entrustOrderId);
        sampleLambdaUpdateWrapper.set(Sample::getIsEffect, IsEffectEnum.DELETE.getCode());
        sampleLambdaUpdateWrapper.set(Sample::getUpdateBy, loginUser.getUserid());
        sampleLambdaUpdateWrapper.set(Sample::getUpdateTime, new Date());
        sampleLambdaUpdateWrapper.set(Sample::getUpdateName, loginUser.getRealName());



        sampleMapper.update(null,sampleLambdaUpdateWrapper);
    }



    /**
     * 查询指定委托单关联的样本
     */
    @DS("slave_1")
    public List<Sample> selectListByEntrustOrderId(Long entrustOrderId) {

        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();

        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, entrustOrderId);

        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());

        sampleLambdaQueryWrapper.orderByAsc(Sample::getId);

        // 查询改委托单的样本信息
        List<Sample> sampleList = sampleMapper.selectList(sampleLambdaQueryWrapper);

        return sampleList;
    }



    /**
     * 查询指定委托单拥有的样本形式列表
     */

    @DS("slave_1")
    public List<Integer> selectSampleFormListByEntrustOrderId(Long entrustOrderId){


        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();

        sampleLambdaQueryWrapper.select(Sample::getSampleForm);

        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, entrustOrderId);

        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());

        sampleLambdaQueryWrapper.groupBy(Sample::getSampleForm);

        List<Sample> sampleList = sampleMapper.selectList(sampleLambdaQueryWrapper);



        return sampleList.stream().map(Sample::getSampleForm).collect(Collectors.toList());


    }




    /**
     * 分页查询指定委托单的独立样本信息
     */
    @DS("slave_1")
    public PcsResult<Page<Sample>> independentSampleListPage(IndependentSampleListPageForm form) {

        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();
        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, form.getEntrustOrderId());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getSampleForm, SampleFormEnum.INDEPENDENT_SAMPLE.getCode());
        sampleLambdaQueryWrapper.orderByAsc(Sample::getId);

        Page<Sample> samplePage = sampleMapper.selectPage(new Page<>(form.getPageIndex(), form.getPageSize()), sampleLambdaQueryWrapper);

        return Result.ok(samplePage);
    }



    /**
     * 查询指定委托单的板号列表
     */
    @DS("slave_1")
    public PcsResult<List<String>> plateList(SamplePlateListForm form) {

        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();
        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, form.getEntrustOrderId());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getSampleForm, SampleFormEnum.PLATE_96.getCode());
        sampleLambdaQueryWrapper.groupBy(Sample::getPlateNum);
        sampleLambdaQueryWrapper.select(Sample::getPlateNum);

        List<Sample> sampleList = sampleMapper.selectList(sampleLambdaQueryWrapper);

        List<String> plateList = sampleList.stream().map(Sample::getPlateNum).collect(Collectors.toList());

        return Result.ok(plateList);
    }




    /**
     * 分页查询指定委托单指定板的样本
     */
    @DS("slave_1")
    public PcsResult<Page<Sample>> plateSampleListPage(PlateSampleListPageForm form) {

        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();
        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, form.getEntrustOrderId());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getSampleForm, SampleFormEnum.PLATE_96.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getPlateNum, form.getPlateNum());
        sampleLambdaQueryWrapper.orderByAsc(Sample::getId);

        Page<Sample> samplePage = sampleMapper.selectPage(new Page<>(form.getPageIndex(), form.getPageSize()), sampleLambdaQueryWrapper);





        return Result.ok(samplePage);
    }



    /**
     * 查询指定委托单的样本形式和样本数量
     */
    @DS("slave_1")
    public PcsResult<SampleFormAndCountVO> getSampleFormAndCount(SampleFormAndCountForm form) {

        int totalNum = 0;
        List<Integer> sampleFormList = Lists.newArrayList();
        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();


        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, form.getEntrustOrderId());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getSampleForm, SampleFormEnum.INDEPENDENT_SAMPLE.getCode());
        sampleLambdaQueryWrapper.select(Sample::getSampleForm);

        Long independentSampleNum = sampleMapper.selectCount(sampleLambdaQueryWrapper);


        sampleLambdaQueryWrapper.clear();

        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, form.getEntrustOrderId());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getSampleForm, SampleFormEnum.PLATE_96.getCode());

        Long plateSampleNum = sampleMapper.selectCount(sampleLambdaQueryWrapper);

        // 有独立样本
        if (independentSampleNum > 0) {
            totalNum += independentSampleNum;
            sampleFormList.add(SampleFormEnum.INDEPENDENT_SAMPLE.getCode());
        }


        // 有96孔板样本
        if (plateSampleNum > 0) {
            totalNum += plateSampleNum;
            sampleFormList.add(SampleFormEnum.PLATE_96.getCode());
        }


        SampleFormAndCountVO sampleFormAndCountVO = new SampleFormAndCountVO();
        sampleFormAndCountVO.setSampleCount(totalNum);
        sampleFormAndCountVO.setSampleFormList(sampleFormList);


        sampleLambdaQueryWrapper.clear();
        sampleLambdaQueryWrapper.eq(Sample::getEntrustOrderId, form.getEntrustOrderId());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.last("limit 1");
        Sample sample = sampleMapper.selectOne(sampleLambdaQueryWrapper);

        if (sample.getStoreTime() != null) {
            sampleFormAndCountVO.setStoreTime(DateUtil.format(sample.getStoreTime(), ProjectConstant.DATE_FORMAT));
        }


        return Result.ok(sampleFormAndCountVO);
    }




    /**
     * 批量导入样本
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult batchImportSample(SampleBatchImportForm form, LoginUser loginUser) {



        EntrustOrder entrustOrder = entrustOrderMapper.selectById(form.getEntrustOrderId());
        if (entrustOrder == null){
            return Result.error(PcsResultCode.ENTRUST_ORDER_NOT_EXIST);
        }

        FileRecord fileRecord = fileRecordMapper.selectById(form.getFileId());

        if (fileRecord == null){
            return Result.error(PcsResultCode.FILE_NOT_EXIST);
        }


        // 读取文件
        try(FileInputStream fileInputStream = new FileInputStream(fileRecord.getAttachmentUrl())){


            List<Sample> sampleList = ExcelUtil.readFromStream(fileInputStream, Sample.class, 0);


            if (CollUtil.isEmpty(sampleList)){
                // 读取的条数为空直接返回
                return Result.ok();
            }


            // 检查板号是否一致
            Set<String> plateSet = sampleList.stream().map(Sample::getPlateNum).collect(Collectors.toSet());
            if (plateSet.size()>1){
                // 不止一个板号
                return Result.error(PcsResultCode.SAMPLE_PLATE_NUM_NOT_CONSISTENT);
            }


            // 申请样本编号
            List<String> sampleCodeList = SequenceUtil.getNextBatchDynamicFormattedValues(
                    SequenceNameEnum.SAMPLE_CODE.getMonthlyNameGenerator(),
                    SequenceNameEnum.SAMPLE_CODE.getFormatter(), sampleList.size());


            // 申请条形码
            String barCode = IdUtil.getSnowflakeNextIdStr();




            int index = 0;


            for(Sample sample : sampleList){
                if (StrUtil.isNotBlank(sample.getPlateNum())){
                    sample.setSampleForm(SampleFormEnum.PLATE_96.getCode());
                    plateSet.add(sample.getPlateNum());
                    sample.setPositionNum(getSamplePlatePositionNum(index));
                }else {
                    sample.setSampleForm(SampleFormEnum.INDEPENDENT_SAMPLE.getCode());
                }

                sample.setId(IdUtil.getSnowflakeNextId());
                sample.setBarcode(barCode);
                sample.setSampleCode(sampleCodeList.get(index++));
                sample.setEntrustOrderId(entrustOrder.getId());
                sample.setEntrustOrderNo(entrustOrder.getEntrustOrderNo());
                sample.setStatus(SampleStatusEnum.PENDING.getCode());
                // 原始样本
                sample.setParentId(0L);
                sample.setIsEffect(IsEffectEnum.NORMAL.getCode());
                sample.setCreateBy(loginUser.getUserid());
                sample.setCreateName(loginUser.getRealName());
                sample.setCreateTime(new Date());
                sample.setUpdateBy(loginUser.getUserid());
                sample.setUpdateName(loginUser.getRealName());
                sample.setUpdateTime(new Date());

            }

            sampleMapper.batchInsert(sampleList);


        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return Result.ok();
    }


    /**
     * 为批量导入的样本生成位置编号
     * @param index
     * @return
     */
    private String getSamplePlatePositionNum(int index) {

        List<String> rowFlag = Lists.newArrayList("A", "B", "C", "D", "E", "F","G","H");

        int row = index / 12;

        return rowFlag.get(row) + (index % 12 + 1);



    }


    /**
     * 物理删除样本
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult physicalDelIndependentSample(SamplePhysicalDeleteForm form) {


        Sample sample = sampleMapper.selectById(form.getId());

        if (sample == null){
            // 样本为空直接返回
            return Result.ok();
        }

        // 校验委托单状态

        EntrustOrder entrustOrder = entrustOrderMapper.selectById(sample.getEntrustOrderId());

        // 只有关联的委托单的状态为待提交才可以物理删除
        if (entrustOrder != null && entrustOrder.getStatus()!= EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()){
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }

        // 物理删除
        sampleMapper.deleteById(form.getId());


        return Result.ok();
    }



    /**
     * 物理删除整个96孔板的样本
     */
    @LhTransaction
    @DS("master_1")
    public PcsResult physicalDelPlateSample(PlateSamplePhysicalDelForm form) {

        LambdaQueryWrapper<Sample> sampleLambdaQueryWrapper = Wrappers.lambdaQuery();


        sampleLambdaQueryWrapper.eq(Sample::getPlateNum, form.getPlateNum());
        sampleLambdaQueryWrapper.eq(Sample::getIsEffect, IsEffectEnum.NORMAL.getCode());
        sampleLambdaQueryWrapper.eq(Sample::getSampleForm, SampleFormEnum.PLATE_96.getCode());
        sampleLambdaQueryWrapper.select(Sample::getId, Sample::getEntrustOrderId);

        List<Sample> sampleList = sampleMapper.selectList(sampleLambdaQueryWrapper);

        if (CollUtil.isEmpty(sampleList)){
            // 样本为空直接返回
            return Result.ok();
        }

        // 校验委托单状态
        EntrustOrder entrustOrder = entrustOrderMapper.selectById(sampleList.get(0).getEntrustOrderId());

        // 只有关联的委托单的状态为待提交才可以物理删除
        if (entrustOrder != null && entrustOrder.getStatus()!= EntrustOrderStatusEnum.PENDING_SUBMISSION.getCode()){
            return Result.error(PcsResultCode.ENTRUST_ORDER_STATUS_ERROR);
        }

        // 物理删除 获取id集合
        List<Long> ids = sampleList.stream().map(Sample::getId).collect(Collectors.toList());

        sampleMapper.deleteBatchIds(ids);

        return Result.ok();
    }
}


