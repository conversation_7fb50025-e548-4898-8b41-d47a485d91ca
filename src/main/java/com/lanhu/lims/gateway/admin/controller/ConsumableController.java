package com.lanhu.lims.gateway.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanhu.lims.gateway.admin.annotation.Log;
import com.lanhu.lims.gateway.admin.auth.constants.SecurityConstants;
import com.lanhu.lims.gateway.admin.auth.context.SecurityContextHolder;
import com.lanhu.lims.gateway.admin.core.BaseController;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.enums.LogBusinessType;
import com.lanhu.lims.gateway.admin.enums.LogOperatorType;
import com.lanhu.lims.gateway.admin.model.Consumable;
import com.lanhu.lims.gateway.admin.model.ConsumableSpecification;
import com.lanhu.lims.gateway.admin.model.LoginUser;
import com.lanhu.lims.gateway.admin.service.ConsumableService;
import com.lanhu.lims.gateway.admin.vo.req.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/********************************
 * @title ConsumableController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description description
 *
 * <AUTHOR> Assistant
 * @date 2025/6/17
 * @version 0.0.1
 *********************************/
@RestController
@Api(tags = "耗材相关接口", value = "耗材相关接口")
public class ConsumableController extends BaseController {

    @Resource
    private ConsumableService consumableService;

    /**
     * 查询耗材列表
     */
    @ApiOperation(value = "查询耗材列表")
    @PostMapping("/consumable/v1/list")
    public PcsResult<List<Consumable>> list(@Validated @RequestBody ConsumableListForm form) {
        return consumableService.list(form);
    }

    /**
     * 查询耗材分页列表
     */
    @ApiOperation(value = "查询耗材分页列表")
    @PostMapping("/consumable/v1/listPage")
    public PcsResult<IPage<Consumable>> listPage(@Validated @RequestBody ConsumableListPageForm form) {
        return consumableService.listPage(form);
    }

    /**
     * 查询耗材详情
     */
    @ApiOperation(value = "查询耗材详情")
    @PostMapping("/consumable/v1/detail")
    public PcsResult<Consumable> detail(@Validated @RequestBody ConsumableSingleForm form) {
        return consumableService.detail(form);
    }

    /**
     * 新增耗材
     */
    @ApiOperation(value = "新增耗材")
    @PostMapping("/consumable/v1/add")
    @Log(title = "新增耗材", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> add(@Validated @RequestBody ConsumableAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.add(form, loginUser.getSysUser());
    }

    /**
     * 修改耗材
     */
    @ApiOperation(value = "修改耗材")
    @PostMapping("/consumable/v1/edit")
    @Log(title = "修改耗材", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> edit(@Validated @RequestBody ConsumableEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.edit(form, loginUser.getSysUser());
    }

    /**
     * 删除耗材
     */
    @ApiOperation(value = "删除耗材")
    @PostMapping("/consumable/v1/del")
    @Log(title = "删除耗材", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> del(@Validated @RequestBody ConsumableSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.del(form, loginUser.getSysUser());
    }

    /**
     * 启用耗材
     */
    @ApiOperation(value = "启用耗材")
    @PostMapping("/consumable/v1/enable")
    @Log(title = "启用耗材", businessType = LogBusinessType.ENABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> enable(@Validated @RequestBody ConsumableSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.enable(form, loginUser.getSysUser());
    }

    /**
     * 禁用耗材
     */
    @ApiOperation(value = "禁用耗材")
    @PostMapping("/consumable/v1/disable")
    @Log(title = "禁用耗材", businessType = LogBusinessType.DISABLE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> disable(@Validated @RequestBody ConsumableSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.disable(form, loginUser.getSysUser());
    }

    /**
     * 查询耗材规格分页列表
     */
    @ApiOperation(value = "查询耗材规格分页列表")
    @PostMapping("/consumable/specification/v1/listPage")
    public PcsResult<IPage<ConsumableSpecification>> listSpecificationPage(@Validated @RequestBody ConsumableSpecificationListPageForm form) {
        return consumableService.listPageSpecifications(form);
    }

    /**
     * 查询耗材规格列表
     */
    @ApiOperation(value = "查询耗材规格列表")
    @PostMapping("/consumable/specification/v1/list")
    public PcsResult<List<ConsumableSpecification>> listSpecifications(@Validated @RequestBody ConsumableSpecificationListForm form) {
        return consumableService.listSpecifications(form);
    }

    /**
     * 新增耗材规格
     */
    @ApiOperation(value = "新增耗材规格")
    @PostMapping("/consumable/specification/v1/add")
    @Log(title = "新增耗材规格", businessType = LogBusinessType.INSERT, operatorType = LogOperatorType.PC)
    public PcsResult<Void> addSpecification(@Validated @RequestBody ConsumableSpecificationAddForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.addSpecification(form, loginUser.getSysUser());
    }

    /**
     * 修改耗材规格
     */
    @ApiOperation(value = "修改耗材规格")
    @PostMapping("/consumable/specification/v1/edit")
    @Log(title = "修改耗材规格", businessType = LogBusinessType.UPDATE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> editSpecification(@Validated @RequestBody ConsumableSpecificationEditForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.editSpecification(form, loginUser.getSysUser());
    }

    /**
     * 删除耗材规格
     */
    @ApiOperation(value = "删除耗材规格")
    @PostMapping("/consumable/specification/v1/del")
    @Log(title = "删除耗材规格", businessType = LogBusinessType.LOGICDELETE, operatorType = LogOperatorType.PC)
    public PcsResult<Void> delSpecification(@Validated @RequestBody ConsumableSpecificationSingleForm form) {
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        return consumableService.delSpecification(form, loginUser.getSysUser());
    }
}
