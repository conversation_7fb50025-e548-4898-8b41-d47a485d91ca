<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.ReagentInventoryMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.ReagentInventory">
        <!--@mbg.generated-->
        <!--@Table t_reagent_inventory-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="reagent_bottle_id" jdbcType="VARCHAR" property="reagentBottleId"/>
        <result column="reagent_code" jdbcType="VARCHAR" property="reagentCode"/>
        <result column="reagent_name" jdbcType="VARCHAR" property="reagentName"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="catalog_number" jdbcType="VARCHAR" property="catalogNumber"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="inventory_total" jdbcType="DECIMAL" property="inventoryTotal"/>
        <result column="inventory_balance" jdbcType="DECIMAL" property="inventoryBalance"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, reagent_bottle_id, reagent_code, reagent_name, short_name, brand, catalog_number,
        specification, unit, inventory_total, inventory_balance, is_effect, create_by, create_name,
        create_time, update_by, update_name, update_time
    </sql>
</mapper>