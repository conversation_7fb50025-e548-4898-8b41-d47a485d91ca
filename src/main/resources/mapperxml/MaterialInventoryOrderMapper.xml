<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanhu.lims.gateway.admin.mapper.MaterialInventoryOrderMapper">
    <resultMap id="BaseResultMap" type="com.lanhu.lims.gateway.admin.model.MaterialInventoryOrder">
        <!--@mbg.generated-->
        <!--@Table t_material_inventory_order-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="inventory_id" jdbcType="BIGINT" property="inventoryId"/>
        <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
        <result column="change_inventory" jdbcType="DECIMAL" property="changeInventory"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_effect" jdbcType="INTEGER" property="isEffect"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_short_name" jdbcType="VARCHAR" property="materialShortName"/>
        <result column="material_brand" jdbcType="VARCHAR" property="materialBrand"/>
        <result column="material_catalog_number" jdbcType="VARCHAR" property="materialCatalogNumber"/>
        <result column="material_specification" jdbcType="VARCHAR" property="materialSpecification"/>
        <result column="material_unit" jdbcType="VARCHAR" property="materialUnit"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="material_type" jdbcType="INTEGER" property="materialType"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="store_position" jdbcType="VARCHAR" property="storePosition"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, inventory_id, operation_type, change_inventory, remark, is_effect, material_name,
        material_code, material_short_name, material_brand, material_catalog_number, material_specification,
        material_unit, create_by, create_name, create_time, update_by, update_name, update_time,
        `status`, material_type, batch_id, store_position
    </sql>
</mapper>